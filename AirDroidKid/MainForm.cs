﻿using iTong.Android;
using iTong.Android.Wave;
using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Device;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AirDroidKid
{
    public partial class MainForm : frmBase
    {
        private static List<MainForm> mListMainForm = new List<MainForm>();

        private static Color mDefaultPanelskBorderStrokeColor = Color.FromArgb(228, 230, 234);

        private Color mWhiteColor = Color.White;

        private static skBorderRadius mRightAngle = new skBorderRadius(0);

        private static skBorderType mPanelBorderType = skBorderType.Top;

        private string mCurrentLangDisplayName = string.Empty;

        private string mIosMail = string.Empty;

        private string mConnectorDeviceId = string.Empty;

        private SelectDeviceMode mSelectDeviceMode = SelectDeviceMode.Normal;

        /// <summary>安卓usb注册设备步骤</summary>
        private enum AndroidUsbTip
        {
            One,
            Two,
            Three,
            Four
        }
        private AndroidUsbTip mAndroidUsbTip = AndroidUsbTip.One;
        private skTimer mAndroidTime = null;

        private frmPopup frmInstallKidTip = null;

        private frmFeedback frmFeedback;

        private skTimer mTimer = null;

        private tdActionHelper<tdActionItemForKid> mActionHelper = tdActionHelper<tdActionItemForKid>.Instance();

        public static MainForm ShowForm(bool topMost = false)
        {
            MainForm currentForm = null;

            foreach (var form in mListMainForm)
            {
                if (form is MainForm mainForm)
                {
                    currentForm = mainForm;
                }
            }

            if (currentForm == null)
            {
                currentForm = new MainForm();
            }

            currentForm.Show();
            currentForm.ShowInTaskbar = true;

            currentForm.Activate();
            currentForm.TopMost = true;
            currentForm.TopMost = false;
            return currentForm;
        }

        public static MainForm GetMainForm()
        {
            MainForm currentForm = null;

            foreach (var form in mListMainForm)
            {
                if (form is MainForm mainForm)
                {
                    currentForm = mainForm;
                }
            }

            return currentForm;
        }

        public static string CurrentLangDisplayName;

        public override skSplit skTransparentImageSplit => new skSplit(15);


        public MainForm()
        {
            PageHelper.InitCef();

            mListMainForm.Add(this);

            InitializeComponent();

            this.mCurrentLangDisplayName = LanguageInterface.Instance().CurrentLanguage.LangDisplayName;

            this.skShowButtonMin = true;

            this.btnClose.skBackgroundImage = Properties.Resources.btn_close_4;
            this.btnClose.skBackgroundImageState = skImageState.FourState;
            this.btnClose.skShowIcon = false;

            this.btnMin.skBackgroundImage = Properties.Resources.btn_hide_4;
            this.btnMin.skBackgroundImageState = skImageState.FourState;
            this.btnMin.skShowIcon = false;

            this.btnClose.skBackgroundImage = Properties.Resources.btn_close_4;
            this.btnClose.skBackgroundImageState = skImageState.FourState;
            this.btnClose.skShowIcon = false;

            this.btnMin.skBackgroundImage = Properties.Resources.btn_hide_4;
            this.btnMin.skBackgroundImageState = skImageState.FourState;
            this.btnMin.skShowIcon = false;

            this.Icon = Properties.Resources.airdroid;
            this.skIcon = Properties.Resources.logo_all.ToBitmap();
            this.ShowIcon = true;
            this.skIconSize = new Size(24, 24);

            this.skTitle = this.Language.GetString("Parental_Connect_home_title"); //AirDroid Parental Connector
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
            this.skTitleColor = Color.FromArgb(45, 47, 51);
            this.skBackgroundColor = mWhiteColor;
            this.skStatusBarBackgroundColor = Color.FromArgb(228, 230, 234);

            MyForm.SetOutScreen(this);

            iTunesHelper.CheckInit();
            SetDesktopIcon();
            // 开启下载vendors.ini和nms配置
            ThreadMgr.Start(() =>
            {
                try
                {
                    MyAirPlay.DownloadOnlineNmsSet();
                    MyAirPlay.DownloadVendorsIni("");
                }
                catch(Exception ex)
                {
                    Common.LogException(ex, "MainForm.Start");
                }
            });
        }

        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 0x219)
            {
                MyAirPlay.LogUsbAndroid("-------------------Usb Channge Msg!!!---------------------");
                //WM_DEVICECHANGE = 0x219,
                //USB设备拔插消息
                MyAirPlay.LoadUsbDevice(false);
            }

            base.WndProc(ref m);
        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.InitStyle();

                this.skTitleBarHeight = 40;

                this.skStatusBarHeight = 0;

                this.NotifyIcon.Icon = Properties.Resources.airdroid;
                this.NotifyIcon.Text = this.Language.GetString("Parental_Connect_home_title");  //AirDroid Parental Connector

                this.tsmiExit.Text = this.Language.GetString("Login.Button.Exit"); // 退出
                this.tsmFeedback.Text = this.Language.GetString("Business_Feedback_logs_title"); // 反馈 | 日志

                this.skTransparentImagePadding = new Padding(6, 6, 6, 6);
                this.skTransparentImage = MyResource.GetImage("frm_bg_shadow");
                this.skBorderType = skBorderType.Round;
                this.skBorderStrokeColor = mWhiteColor;
                this.skBorderRadius = new skBorderRadius(2);

                string strModify = string.Empty;
                if (MyLog.IsTestMode)
                    strModify = string.Format(" ({0})", new FileInfo(Application.ExecutablePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm"));
                MyLog.LogFile($"our programe is starting!!! beta:001", "ConnectChanged");

                this.lblVersion.skText = string.Format("v{0}{1}", Common.GetSoftVersion(), strModify);

                MyTest.Callback += OnTestCallback;
                MyTest.InitTestMenu(this.cmsBottom);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MainForm.InitControls");
            }
        }

        protected void OnTestCallback(object sender, TestArg e)
        {
            this.lblInTestMode.Top = this.skTitleBarHeight;
            this.lblInTestMode.Visible = e.Visible;
            this.lblInTestMode.skText = e.Title;
            this.lblInTestMode.Location = new Point((this.Width - this.lblInTestMode.Width) / 2, (this.skTitleBarHeight - this.lblInTestMode.Height) / 2);

            this.btnUploadLog.Visible = e.Visible;
            this.mStatusBarLableText = e.Title;
        }

        private void InitStyle()
        {
            try
            {
                this.Size = new Size(940, 580);
                Size size = Screen.PrimaryScreen.WorkingArea.Size;
                this.Left = (size.Width - Width) / 2;
                this.Top = (size.Height - Height) / 2;
                this.WindowState = FormWindowState.Normal;

                this.btnLanguageSetting.skAutoSize = true;
                this.btnLanguageSetting.MinimumSize = new Size(75, 28);
                this.btnLanguageSetting.skBorderRadius = new skBorderRadius(12);

                this.btnLanguageSetting.skText = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
                this.btnLanguageSetting.Location = new Point(this.cbsplit.Location.X - 7 - this.btnLanguageSetting.Width, 6);

                this.btnFeedBack.Location = new Point(this.btnLanguageSetting.Location.X - 2 - this.btnFeedBack.Width, 9);

                this.pnlSelectSituation.skBorderType = mPanelBorderType;
                this.pnlFirstGuide.skBorderType = mPanelBorderType;
                this.pnlConnectingGuide.skBorderType = mPanelBorderType;

                this.pnlSelectSituation.skBorderRadius = mRightAngle;
                this.pnlFirstGuide.skBorderRadius = mRightAngle;
                this.pnlConnectingGuide.skBorderRadius = mRightAngle;

                this.pnlSelectSituation.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlFirstGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlConnectingGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

                this.pnlSelectSituationEnable.skBorderRadius = new skBorderRadius(16);
                this.pnlSelectSituationDisable.skBorderRadius = new skBorderRadius(16);
                this.pnlSelectSituationDisableSub.skBorderRadius = new skBorderRadius(10);
                this.pnlSelectSituationEnableSub.skBorderRadius = new skBorderRadius(10);

                this.lblFirstGuideTitle.skText = "    " + this.Language.GetString("ready_to_start"); //准备开始
                this.lblFirstGuideContent.skText = this.Language.GetString("before_start_connect_use_title"); //开始之前请使用 USB 连接孩子设备

                this.lblFirstGuideNote1.skText = $"{this.Language.GetString("ensure_device_unlocked_title")}"; // 请确保您已经解锁了设备屏幕
                this.lblFirstGuideNote2.skText = $"{this.Language.GetString("ensure_usb_connection_title")}";// 请确保 USB 连接线没有问题
                this.lblFirstGuideNote3.skText = $"{this.Language.GetString("ensure_Android_File_Transfer_title")}";// Android 设备请选择【传输文件】作为USB连接模式
                this.lblFirstGuideNote4.skText = $"{this.Language.GetString("ensure_iOS_device_trust_title")}";// iOS 设备请在屏幕弹窗上选择【信任】
                this.lblFirstGuideNote5.skText = $"{this.Language.GetString("ensure_iOS_Uninstall_itunes_title")}";  // 若始终无法识别 iOS 设备，请卸载电脑中的 iTunes 后重启 Connector
                this.btnFirstGuideLearnMore.skText = string.Concat(this.Language.GetString("help_center_title"), " >"); // 帮助中心

                this.lblSelectSituationTitle.skText = this.Language.GetString("Parental_Connect_home_select"); //请选择开启/解除监督模式
                this.lblSelectSituationEnableDescribe.skText = this.Language.GetString("Parental_Connect_home_open"); //开启 iOS 设备监督模式
                this.lblSelectSituationDisableDescribe.skText = this.Language.GetString("Parental_Connect_home_close"); //解除 iOS 设备监督模式

                this.lblEnableSupervisionModeContent.skText = this.Language.GetString("APC_EnableSupervision_Description"); //开启后即可体验完整模式的 AirDroid Parental Control 功能，帮助您更好地管控儿童设备。
                this.lblDisableSupervisionModeContent.skText = this.Language.GetString("APC_DisableSupervision_Description"); //当您不再需要 AirDroid Parental Control 帮助您管理儿童设备时再关闭监督模式。

                this.lblSelectSituationEnableDescribe.Padding = new Padding(0, 0, 0, 5);
                this.lblSelectSituationDisableDescribe.Padding = new Padding(0, 0, 0, 5);

                this.InitPanel(this.pnlFirstGuide);
                this.ShowFirstGuideHandle();

                #region 边框类型

                this.pnlCloseFindMyiPhoneTip.skBorderType = mPanelBorderType;

                this.pnlUsbConnectTip.skBorderType = mPanelBorderType;
                this.pnlConnecting.skBorderType = mPanelBorderType;
                this.pnlConnectionFailed.skBorderType = mPanelBorderType;
                this.pnlDeviceNotDetected.skBorderType = mPanelBorderType;

                this.pnlEnableConnectedSuccess.skBorderType = mPanelBorderType;
                this.pnlActivatingSupervisionMode.skBorderType = mPanelBorderType;
                this.pnlEnableSupervisionModeSuccess.skBorderType = mPanelBorderType;

                this.pnlDisableConnectedSuccess.skBorderType = mPanelBorderType;
                this.pnlDeactivatingSupervisionMode.skBorderType = mPanelBorderType;
                this.pnlDisableSupervisionModeSuccess.skBorderType = mPanelBorderType;

                #endregion 边框类型

                #region 边框圆角

                this.pnlCloseFindMyiPhoneTip.skBorderRadius = mRightAngle;

                this.pnlUsbConnectTip.skBorderRadius = mRightAngle;
                this.pnlConnecting.skBorderRadius = mRightAngle;
                this.pnlConnectionFailed.skBorderRadius = mRightAngle;
                this.pnlDeviceNotDetected.skBorderRadius = mRightAngle;

                this.pnlEnableConnectedSuccess.skBorderRadius = mRightAngle;
                this.pnlActivatingSupervisionMode.skBorderRadius = mRightAngle;
                this.pnlEnableSupervisionModeSuccess.skBorderRadius = mRightAngle;

                this.pnlDisableConnectedSuccess.skBorderRadius = mRightAngle;
                this.pnlDeactivatingSupervisionMode.skBorderRadius = mRightAngle;
                this.pnlDisableSupervisionModeSuccess.skBorderRadius = mRightAngle;

                #endregion 边框圆角

                #region 边框颜色

                this.pnlCloseFindMyiPhoneTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

                this.pnlUsbConnectTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlConnecting.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlConnectionFailed.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlDeviceNotDetected.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

                this.pnlEnableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlActivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlEnableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

                this.pnlDisableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlDeactivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
                this.pnlDisableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

                #endregion 边框颜色

                #region Android usb 注册页面
                this.lblAndroidConnectingGuideTitle.skText = this.Language.GetString("Parental_Connect_USBdebugging_title"); // 请按以下步骤配置您的设备
                this.lblAndroidConnectingGuideVideoLink.skText = this.Language.GetString("USBdebugging_Video_tutorial");  // 查看视频教程
                #endregion

                this.lblUsbConnectTipTitle.skText = this.Language.GetString("Parental_Connect_Usb_title"); //请将设备通过USB与电脑进行连接
                this.lblUsbConnectTipDescribe.skText = this.Language.GetString("Parental_Connect_Usb_text1"); //开启监督模式以使用 AirDroid Parental Control 的完整功能，支持禁用设备和限制应用等等
                this.lblUsbConnectTipNotice.skText = this.Language.GetString("Parental_Connect_Usb_note"); //注意:\r\n1.确保你已经解锁了设备屏幕。\r\n2.请检查USB连接线
                this.btnUsbConnectQuestion.skText = this.Language.GetString("Parental_Connect_Usb_buttion_nodevice"); //未检测到设备？

                this.lblDeviceNotDetectedTip1.skBorderRadius = new skBorderRadius(12);
                this.lblDeviceNotDetectedTip2.skBorderRadius = new skBorderRadius(12);
                this.lblDeviceNotDetectedTip3.skBorderRadius = new skBorderRadius(12);

                this.lblDeviceNotDetectedTitle.skText = this.Language.GetString("Parental_Connect_nodevice_tilte"); //未检测到设备，请按照以下步骤重试
                this.lblDeviceNotDetectedMethod1.skText = this.Language.GetString("Parental_Connect_nodevice_text1"); //输入设备密码
                this.lblDeviceNotDetectedMethod2.skText = this.Language.GetString("Parental_Connect_nodevice_text2"); //在屏幕的弹窗上选择【信任】
                this.lblDeviceNotDetectedMethod3.skText = this.Language.GetString("Parental_Connect_nodevice_text3"); //请换一条 USB 数据线或换一个 USB 接口

                this.lblConnectingTitle.skText = this.Language.GetString("Parental_Connect_connectdevice_title"); //正在连接设备......
                this.lblConnectingDescribe.skText = this.Language.GetString("Parental_Connect_connectdevice_text"); //请保持 USB 连接不要断开

                this.lblConnectingGuide1.skBorderRadius = new skBorderRadius(12);
                this.lblConnectingGuide2.skBorderRadius = new skBorderRadius(12);

                this.lblConnectingGuideTitle.skText = this.Language.GetString("Parental_Connect_trusteddevice_title"); //请在设备上选择【信任】这台电脑，并完成后续步骤输入设备密码
                this.lblConnectingGuideStep1.skText = this.Language.GetString("Parental_Connect_nodevice_text2");// 在屏幕的弹窗上选择【信任】
                this.lblConnectingGuideStep2.skText = this.Language.GetString("Parental_Connect_nodevice_text1"); //输入设备密码

                this.lblConnectionFailedTitle.skText = this.Language.GetString("Common.ConnectFailed"); //连接失败
                this.lblConnectionFailedNote.skText = this.Language.GetString("Parental_Connect_Usb_fail_note"); //注意：\r\n请保持您的设备始终处于连接状态
                this.btnConnectionFailedTryAgain.skBorderRadius = new skBorderRadius(18);
                this.btnConnectionFailedTryAgain.skText = this.Language.GetString("Common_Tryagain_button"); //重试

                this.pnlEnableConnectedSuccessDeviceMsg.skBorderRadius = new skBorderRadius(16);
                this.btnEnableConnectedSuccessDeviceNameTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicename"); // 设备名称：
                this.btnEnableConnectedSuccessDeviceModelTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicemodel"); // 设备型号：
                this.btnEnableConnectedSuccessDeviceMailTip.skText = this.Language.GetString("binding_account_body"); // 绑定账号：

                this.btnStartEnableSupervisionMode.skText = this.Language.GetString("Parental_Connect_Usb_success_button_start"); //开始
                this.btnStartEnableSupervisionMode.skBorderRadius = new skBorderRadius(18);

                this.pnlActivatingSupervisionModeDeviceMsg.skBorderRadius = new skBorderRadius(16);
                this.btnActivatingSupervisionModeDeviceNameTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicename"); // 设备名称：
                this.btnActivatingSupervisionModeDeviceModelTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicemodel");// 设备型号：
                this.btnActivatingSupervisionModeDeviceMailTip.skText = this.Language.GetString("binding_account_body");   // 绑定账号：

                this.lblStartSuccessfullyTitle.skText = this.Language.GetString("Parental_Connect_supervisionmode_text1"); //恭喜！您已成功开启监督模式
                this.lblStartSuccessfullyDescribe.skText = this.Language.GetString("turn_off_app_Limits_setting"); //如果您之前正在使用iOS系统的【屏幕使用时间】，为了避免冲突请关闭【App限额】的限制

                this.btnEnableSupervisionModeFinish.skText = this.Language.GetString("bookmark_edit_finish"); //完成
                this.btnEnableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

                this.pnlDisableConnectedSuccessDeviceMsg.skBorderRadius = new skBorderRadius(16);
                this.btnDisableConnectedSuccessDeviceNameTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicename");  // 设备名称：
                this.btnDisableConnectedSuccessDeviceModelTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicemodel");// 设备型号：
                this.btnDisableConnectedSuccessDeviceMailTip.skText = this.Language.GetString("binding_account_body");  // 绑定账号：
                this.lblDisableConnectedSuccessNotice.skText = this.Language.GetString("Parental_Connect_Usb_text2"); // 解除 AirDroid Kids 监督模式并停止相关监管功能

                this.btnStartDisableSupervisionMode.skText = this.Language.GetString("Parental_Connect_Usb_success_button_start"); //开始
                this.btnStartDisableSupervisionMode.skBorderRadius = new skBorderRadius(18);

                this.lblDeactivatingSupervisionModeTitle.skText = this.Language.GetString("Parental_Connect_supervisionmode_close"); //正在解除监督模式
                this.pnlDeactivatingSupervisionModeDeviceMsg.skBorderRadius = new skBorderRadius(16);
                this.btnDeactivatingSupervisionModeDeviceNameTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicename"); // 设备名称：
                this.btnDeactivatingSupervisionModeDeviceModelTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_devicemodel"); // 设备型号：
                this.btnDeactivatingSupervisionModeDeviceMailTip.skText = this.Language.GetString("binding_account_body");  // 绑定账号：
                this.lblDeactivatingSupervisionModeTip.skText = this.Language.GetString("Parental_Connect_supervisionmode_note"); //您的设备将会重启并更新，整个过程大概需要1分钟请耐心等候。

                this.lblCloseSuccessfullyDescribe.skText = this.Language.GetString("Parental_Connect_supervisionmode_text2"); //恭喜！您已成功解除监督模式

                this.btnDisableSupervisionModeFinish.skText = this.Language.GetString("bookmark_edit_finish"); //完成

                this.lblDriveInstalled.skText = this.Language.GetString("Parental_InstallDrive_successful"); //驱动已安装完成

                //关闭查找我的手机页面
                this.lblCloseFindMyiPhoneTipTitle.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_title");//请按照以下步骤关闭“查找我的 iPhone”
                this.lblCloseFindMyiPhoneTipContent1.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_step1"); //打开“设置>[您的名字]>查找”
                this.lblCloseFindMyiPhoneTipContent2.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_step2"); //选择“查找我的 iPhone”
                this.lblCloseFindMyiPhoneTipContent3.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_step3"); //输入您的 Apple ID 密码将它关闭
                this.SetCloseFindMyiPhoneRecheckLabelStyle();
                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_button1"); //是的，我已关闭

                this.btnCloseFindMyiPhoneTip.skAutoSize = true;
                this.btnCloseFindMyiPhoneTip.MaximumSize = new Size(218, 18);
                this.btnCloseFindMyiPhoneTip.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_tips");//无法关闭“查找我的设备”
                this.btnCloseFindMyiPhoneTip.Location = new Point(this.picCloseFindMyiPhoneTip3.Location.X + this.picCloseFindMyiPhoneTip3.Width / 2 - this.btnCloseFindMyiPhoneTip.Width / 2, this.lblCloseFindMyiPhoneTipContent3.Location.Y + this.lblCloseFindMyiPhoneTipContent3.Height);

                this.btnDisableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

                this.btnLanguageSetting.skBorderRadius = new skBorderRadius(10);

                this.lblInTestMode.Anchor = AnchorStyles.Top | AnchorStyles.Right | AnchorStyles.Left;
                this.lblInTestMode.Size = new Size(130, this.lblInTestMode.Height);
                this.lblInTestMode.Visible = true;

                this.lblVersion.Anchor = AnchorStyles.Bottom | AnchorStyles.Right | AnchorStyles.Left;
                this.lblVersion.skTextFont = MyFont.CreateFont(10, true, true);
                this.lblVersion.Location = new Point(10, this.lblInTestMode.Bottom + 10);
                this.lblVersion.Size = new Size(this.Width - 20, this.lblVersion.Height);
                this.lblVersion.Visible = true;
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "InitStyle");
            }
        }

        /// <summary>首页控件重定位</summary>
        private void ShowFirstGuideHandle()
        {
            try
            {
                this.picFirstGuideTitle.Location = new Point(80, 141);
                this.lblFirstGuideTitle.Location = new Point(80, 141);
                this.lblFirstGuideContent.Location = new Point(80 - 3, this.lblFirstGuideTitle.Bottom + 10);

                this.lblFirstGuideNoteDot1.Location = new Point(74, this.lblFirstGuideContent.Bottom + 19);
                this.lblFirstGuideNote1.Location = new Point(91, this.lblFirstGuideContent.Bottom + 19);
                this.lblFirstGuideNoteDot2.Location = new Point(74, this.lblFirstGuideNote1.Bottom + 3);
                this.lblFirstGuideNote2.Location = new Point(91, this.lblFirstGuideNote1.Bottom + 3);
                this.lblFirstGuideNoteDot3.Location = new Point(74, this.lblFirstGuideNote2.Bottom + 3);
                this.lblFirstGuideNote3.Location = new Point(91, this.lblFirstGuideNote2.Bottom + 3);
                this.lblFirstGuideNoteDot4.Location = new Point(74, this.lblFirstGuideNote3.Bottom + 3);
                this.lblFirstGuideNote4.Location = new Point(91, this.lblFirstGuideNote3.Bottom + 3);
                this.lblFirstGuideNoteDot5.Location = new Point(74, this.lblFirstGuideNote4.Bottom + 3);
                this.lblFirstGuideNote5.Location = new Point(91, this.lblFirstGuideNote4.Bottom + 3);

                this.btnFirstGuideLearnMore.Location = new Point(80 - 2, this.lblFirstGuideNote5.Bottom + 15);

                this.picFirstGuide.Location = new Point(580, 85);
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "ShowFirstGuideHandle");
            }
        }

        /// <summary>选择模式 显示处理</summary>
        private void ShowSelectModeHandle()
        {
            try
            {
                this.lblEnableSupervisionModeContent.Location = new Point(this.lblSelectSituationEnableDescribe.Location.X, this.lblSelectSituationEnableDescribe.Location.Y + this.lblSelectSituationEnableDescribe.Height);
                this.lblDisableSupervisionModeContent.Location = new Point(this.lblSelectSituationDisableDescribe.Location.X, this.lblSelectSituationDisableDescribe.Location.Y + this.lblSelectSituationDisableDescribe.Height);

                int enableSubPanelHeight = this.lblSelectSituationEnableDescribe.Height + this.lblEnableSupervisionModeContent.Height + 35;
                int disableSubPanelHeight = this.lblSelectSituationDisableDescribe.Height + this.lblDisableSupervisionModeContent.Height + 35;
                int subPanelHeight = enableSubPanelHeight >= disableSubPanelHeight ? enableSubPanelHeight : disableSubPanelHeight;

                this.pnlSelectSituationEnableSub.Size = new Size(312, subPanelHeight);
                this.pnlSelectSituationDisableSub.Size = new Size(312, subPanelHeight);

                int enablePanelHeight = this.picSelectSituationEnableImage.Height + this.pnlSelectSituationEnableSub.Height + 50;
                int disablePanelHeight = this.picSelectSituationDisableImage.Height + this.pnlSelectSituationDisableSub.Height + 50;
                int panelHeight = enablePanelHeight >= disablePanelHeight ? enablePanelHeight : disablePanelHeight;

                this.pnlSelectSituationEnable.Size = new Size(340, panelHeight);
                this.pnlSelectSituationDisable.Size = new Size(340, panelHeight);

                this.lblSelectSituationTitle.Location = new Point(this.lblSelectSituationTitle.Location.X,
                    (this.pnlSelectSituation.Height - (this.lblSelectSituationTitle.Height + 25 + this.pnlSelectSituationEnable.Height)) / 2);

                int selectLocationY = this.lblSelectSituationTitle.Location.Y + this.lblSelectSituationTitle.Height + 25;
                this.pnlSelectSituationEnable.Location = new Point(this.pnlSelectSituationEnable.Location.X, selectLocationY);
                this.pnlSelectSituationDisable.Location = new Point(this.pnlSelectSituationDisable.Location.X, selectLocationY);
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "ShowSelectModeHandle");
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            ThreadMgr.Start(this.GetIOSModels);

            MyForm.SetScreenCenter(this, true);

            MyAirPlay.ConnectChanged += MyAirPlay_ConnectChanged;
            MyAirPlay.LoadUsbDevice(false);

            MyUsb.DOCallback += MyUsb_DOCallback;

            this.StartListenHandle();

            this.lblFirstGuideTitle.DoubleClick += this.picDevice_DoubleClick;
            this.picFirstGuide.DoubleClick += this.picDevice_DoubleClick;
            this.lblAndroidConnectingGuideTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblSelectSituationTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblDeactivatingSupervisionModeTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblActivatingSupervisionModeTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblEnableConnectedSuccessTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblDisableConnectedSuccessTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblStartSuccessfullyTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblCloseSuccessfullyDescribe.DoubleClick += this.picDevice_DoubleClick;
            this.lblCloseFindMyiPhoneTipTitle.DoubleClick += this.picDevice_DoubleClick;
            this.lblCloseFindMyiPhoneTipTitle.DoubleClick += this.picDevice_DoubleClick;
        }

        private void picDevice_DoubleClick(object sender, EventArgs e)
        {
            MyTest.SetDoubleClick();
        }

        private void SetControlEnabled(Control ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() =>
                {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        private void tsmiExit_Click(object sender, EventArgs e)
        {
            this.Hide();
            this.ShowInTaskbar = false;

            this.NotifyIcon.Visible = false;

            OnExitApp();
        }

        private void tsmFeedback_Click(object sender, EventArgs e)
        {
            frmFeedback frmFeedback = new frmFeedback();
            frmFeedback.ShowDialog();
        }

        private static void OnExitApp()
        {
            try
            {
                MyLog.WriteLine("OnExitApp -> Kill Process");

                ThreadMgr.Start(() =>
                {
                    Thread.Sleep(500);

                    SocketMgr.KillCurrentProcess();
                });
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "OnExitApp");
            }
        }

        private void NotifyIcon_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            MainForm.ShowForm();
        }

        private void SetControlEnabled(ToolStripMenuItem ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() =>
                {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnable_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationEnableDescribe_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnableSub_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationEnable_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationEnableImage_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>连接引导页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectTipBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>设备未信任提示页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingGuideBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>没有设备？ 引导 按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeviceNotDetectedBack_Click(object sender, EventArgs e)
        {
            if (this.mPreviousPanel != null)
                this.InitPanel(this.mPreviousPanel);
            else
                this.SelectConnectGuide();
        }

        /// <summary>关闭监督模式  完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDisableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.DisableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationDisableImage_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisable_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationDisableDescribe_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisableSub_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationDisable_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>正在连接设备 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>切换语种</summary>
        private void btnLanguageSetting_Click(object sender, EventArgs e)
        {
            //语言界面
            this.cmsLanguage.Items.Clear();

            string shortName;

            foreach (LangInfo info in this.Language.Languages)
            {
                shortName = this.LanguageDisplayNameHandle(info.LangDisplayName);

                // 先移除阿拉伯语
                if (info.LangName == "ar")
                    continue;

                if (info.LangDisplayName == LanguageInterface.Instance().CurrentLanguage.LangDisplayName)
                    this.cmsLanguage.Items.Add(shortName, Properties.Resources.language_selected);
                else
                    this.cmsLanguage.Items.Add(shortName);
            }
            this.cmsLanguage.Show(this.btnLanguageSetting, new Point(0, this.btnLanguageSetting.Height));
        }

        /// <summary>选择语种</summary>
        private void cmsLanguage_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            string selectLanguage = this.LanguageDisplayNameHandle(e.ClickedItem.Text.ToString()); //selectLanguage; 

            string currentLanguageDisplayName = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
            if (selectLanguage == currentLanguageDisplayName)
                return;

            using (frmLanguageChange frmLanguageChange = new frmLanguageChange(selectLanguage))
            {
                if (frmLanguageChange.ShowDialog(this) != DialogResult.OK)
                    return;

                this.btnLanguageSetting.skText = selectLanguage;

                if (this.btnLanguageSetting.skText == currentLanguageDisplayName)
                    return;

                foreach (LangInfo info in this.Language.Languages)
                {
                    if (this.LanguageDisplayNameHandle(info.LangDisplayName) != this.btnLanguageSetting.Text)
                        continue;

                    this.mCurrentLangDisplayName = info.LangDisplayName;
                    IniHelper.SetValue(SettingKey.Language, info.LangName);
                }
                this.RestartApplication();
            }
        }

        /// <summary>开始 开启监督模式</summary>
        private void btnStartEnableSupervisionMode_Click(object sender, EventArgs e)
        {
            if (this.mAndroidDevice != null && this.mAndroidDevice.IsAndroid) // android
            {
                // 按钮打开loading
                this.BtnLoadingAnimation(this.btnStartEnableSupervisionMode, true, "", new Size(24, 24), skImageState.OneState, MyResource.GetImage("start_loading"));

                ThreadMgr.StartWithPara(this.CheckInstallKid, this.mAndroidDevice.DeviceID);
            }
            else // ios
            {
                if (this.mDevice == null)
                {
                    this.SelectConnectGuide();
                }
                else
                {
                    if (this.mDevice.FindMyPhone)
                        this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                    else
                        this.ActivateSuperviseStart();
                }
            }
        }

        /// <summary>开始 关闭监督模式</summary>
        private void btnStartDisableSupervisionMode_Click(object sender, EventArgs e)
        {
            if (this.mDevice == null)
            {
                this.SelectConnectGuide();
            }
            else
            {
                if (this.mDevice.FindMyPhone)
                    this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                else
                    this.DeactivateSuperviseStart();
            }
        }

        /// <summary>开启监督模式 完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEnableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.EnableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>开启监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnActivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接成功 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectedSuccessfullyBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCloseSuperviseBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>关闭监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeactivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectionFailedBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 重试按钮</summary>
        private void btnConnectionFailedTryAgain_Click(object sender, EventArgs e)
        {
            if (this.mAndroidDevice != null && this.mAndroidDevice.IsAndroid) // android
            {
                this.mActionHelper.AddKid(tdActionModeKeyForKid.AndroidDeviceDoReStart);

                this.ActivateSuperviseStart();
            }
            else // ios
            {
                this.mActionHelper.AddKid(tdActionModeKeyForKid.ConnectionRetry);

                if (this.lblConnectionFailedTitle.skText == this.Language.GetString("Common.ConnectFailed")) // 连接失败
                {
                    this.DeviceConnectHandle();
                }
                else
                {
                    if (this.HaveNoDevice())
                    {
                        this.InitPanel(this.pnlUsbConnectTip);
                    }
                    else
                    {
                        if (this.mSelectDeviceMode == SelectDeviceMode.Activating)
                            this.ActivateSuperviseStart();
                        else
                            this.DeactivateSuperviseStart();
                    }
                }
            }
        }

        private string LanguageDisplayNameHandle(string langDisplayName)
        {
            string result = langDisplayName;

            if (!langDisplayName.Contains("中文") && langDisplayName.Contains("("))
                result = langDisplayName.Substring(0, langDisplayName.IndexOf('(')).ToString();

            return result;
        }

        private void pnlSelectSituationEnable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationEnable.Invalidate();
        }

        private void pnlSelectSituationDisable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationDisable.Invalidate();
        }

        private void pnlSelectSituation_MouseEnter(object sender, EventArgs e)
        {
            if (this.pnlSelectSituationEnable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationEnable.Invalidate();
            }

            if (this.pnlSelectSituationDisable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationDisable.Invalidate();
            }
        }

        private void lblSelectSituationTitle_DoubleClick(object sender, EventArgs e)
        {
            this.picDevice_DoubleClick(sender, e);
        }

        private void btnCloseFindMyiPhoneTip_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.UnableToCloseFindMyiPhone);

            Common.OpenUrl("https://help.airdroid.com/hc/articles/26246372283803");
        }

        private void btnCloseFindMyiPhoneBack_Click(object sender, EventArgs e)
        {
            //回到开启/关闭监督模式
            if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                this.InitPanel(this.pnlEnableConnectedSuccess);
            else
                this.InitPanel(this.pnlDisableConnectedSuccess);
        }

        /// <summary>问题反馈</summary>
        private void btnFeedBack_Click(object sender, EventArgs e)
        {
            //Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);

            //if (form == null)
            //{
            //    frmFeedback frmFeedback = new frmFeedback();
            //    frmFeedback.Owner = this;
            //    frmFeedback.ShowDialog();
            //}

            if (PageHelper.HasInitCef)
            {
                Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);
                if (form == null)
                {
                    if (this.frmFeedback == null)
                    {
                        this.frmFeedback = new frmFeedback();
                        this.frmFeedback.Owner = this;
                        this.frmFeedback.FormClosed += frmFeedback_FormClosed;
                        this.frmFeedback.ShowDialog();
                    }
                    else
                    {
                        this.frmFeedback.Activate();
                    }
                }
            }
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectQuestion_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddKid(tdActionModeKeyForKid.UsbConnectNoDevice);
        }

        private void btnConnectionFailedTip_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddKid(tdActionModeKeyForKid.ConnectedFailedNoDevice);
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            //不响应DPI缩放
            MyRegistry.SetDpiAwareness(DpiAwareness.unaware);

            base.OnClosing(e);
        }

        private void btnUploadLog_Click(object sender, EventArgs e)
        {
#if DEBUG
            string dirBackup = @"X:\Backup\00008140-000218C60CF3001C_MiMa_a";
            MBFileSearch keyWord = new MBFileSearch();
            keyWord.LikePath = "CallHistoryDB";
            mbdb.FilterDataFromBackup(dirBackup, "a", keyWord);

            if (this.mDevice != null && this.mDevice.IsConnected)
            {
                if (this.mDevice.ConnectMode == ConnectMode.USB)
                {
                    if (!this.mDevice.GetWirelessBuddyFlags())
                        this.mDevice.SetWirelessBuddyFlags(true);
                }
                else
                {
                    BackupHelper backup = new BackupHelper(this.mDevice);
                    backup.CurrentBackupFolder = @"Y:\iTunesBackup\00008020-000D1C2A0132002E";
                    backup.BackupProgressChanged += OnBackupProgressChanged;
                    backup.StartBackup(false, false);
                }
            }
#else
            MyTest.tsmiUploadLogToJira_Click();
#endif
        }

        private void OnBackupProgressChanged(object sender, BackupArgs e)
        {
            Console.WriteLine(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " OnBackupProgressChanged -> " + e.Progress.ToString("0.0000#"));
        }

        /// <summary>ios使用的设备切换</summary>
        private void cmsEnableConnectedSuccessDevices_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            string selectDeviceName = e.ClickedItem.Text.ToString();

            foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
            {
                if (selectDeviceName != device.DeviceName)
                {
                    continue;
                }
                else
                {
                    this.mDevice = device;
                    string device_id = this.mDevice.DeviceID.Replace("-", "");
                    foreach(AirPlayDevice dev in this.mAndroidDeviceList)
                    {
                        if(device_id == dev.DeviceID)
                        {
                            this.mAndroidDevice = dev;
                            break;
                        }
                    }
                    break;
                }
            }

            if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
            {
                this.btnEnableConnectedSuccessDevice.skText = selectDeviceName;
                this.btnEnableConnectedSuccessDeviceName.skText = this.mDevice.DeviceName;
                this.btnEnableConnectedSuccessDeviceModel.skText = SummaryInfo.FormatProduct(GetIOSProductType(this.mDevice));
                //this.ConnectedSuccessTitleHandle(this.lblEnableConnectedSuccessTitle, selectDeviceName);
            }
            else
            {
                this.btnDisableConnectedSuccessDevice.skText = selectDeviceName;
                this.btnDisableConnectedSuccessDeviceName.skText = this.mDevice.DeviceName;
                this.btnDisableConnectedSuccessDeviceModel.skText = SummaryInfo.FormatProduct(GetIOSProductType(this.mDevice));
                //this.ConnectedSuccessTitleHandle(this.lblDisableConnectedSuccessTitle, selectDeviceName);
            }
        }

        private void btnDisableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //关闭监督模式 设备列表
            this.InitDeviceMenu(this.btnDisableConnectedSuccessDevice, true);
        }

        private void btnEnableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //开启监督模式 设备列表
            this.InitDeviceMenu(this.btnEnableConnectedSuccessDevice, true);
        }

        private void btnTurnOffFindMyiPhone_Click(object sender, EventArgs e)
        {
            if (this.btnTurnOffFindMyiPhone.skText.Equals(this.Language.GetString("Parental_Connect_turnofffindmyiphone_button1"))) //已关闭
                this.mActionHelper.AddKid(tdActionModeKeyForKid.TurnedOffFindMyiPhone);
            else
                this.mActionHelper.AddKid(tdActionModeKeyForKid.RecheckCloseFindMyiPhone);

            if (this.mDevice != null && this.mDevice.FindMyPhone)
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle(true);

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_button2"); //重新检查 

                this.mActionHelper.AddKid(tdActionModeKeyForKid.CloseFindMyiPhoneRecheck);
            }
            else
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle();

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental_Connect_turnofffindmyiphone_button1"); //是的，我已关闭

                //回到开启/关闭监督模式
                if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                    this.ActivateSuperviseStart();
                else
                    this.DeactivateSuperviseStart();
            }
        }

        private void btnFirstGuideLearnMore_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.FirstGuideLearnMore);

            Common.OpenUrl("https://help.airdroid.com/hc/sections/32031961415963");
        }

        private void btnFirstGuideNoDevice_Click(object sender, EventArgs e) // 未引用
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.FirstGuideNoDevice);

            this.InitPanel(this.pnlDeviceNotDetected);
        }

        private void lblEnableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        private void lblDisableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

#region Android系列函数
        /// <summary>android usb设备通知事件</summary>
        private void MyAirPlay_ConnectChanged(object sender, AirPlayDevice e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    this.MyAirPlay_ConnectChanged(sender, e);
                }));
            }
            else
            {
                // 检查设备列表是否包含
                if (this.mAndroidDeviceList != null)
                {
                    bool isExist = false;
                    int index = 0;
                    for (; index < this.mAndroidDeviceList.Count; index++)
                    {
                        if (string.Equals(e.HardwareId, this.mAndroidDeviceList[index].HardwareId))
                        {
                            isExist = true;
                            break;
                        }
                    }
                    if (isExist)
                    {
                        this.mAndroidDeviceList[index] = e;
                        MyLog.LogFile($"{e.DeviceName} {e.State} {e.ConnectMode} is update on list", "ConnectChanged");
                    }
                    else
                    {
                        this.mAndroidDeviceList.Add(e);
                        MyLog.LogFile($"{e.DeviceName} {e.State} {e.ConnectMode} DeviceListCount={this.mAndroidDeviceList.Count} is add on list", "ConnectChanged");
                    }
                }
                else
                {
                    this.mAndroidDeviceList = new List<AirPlayDevice>();
                    this.mAndroidDeviceList.Add(e);
                    MyLog.LogFile($"{e.DeviceName} {e.State} {e.ConnectMode} DeviceListCount={this.mAndroidDeviceList.Count} is add on list", "ConnectChanged");
                }

                // 设备判断
                if (this.mAndroidDevice == null) // 无设备时
                {
                    this.mAndroidDevice = e;
                    MyLog.LogFile($"{this.mAndroidDevice.DeviceName} {this.mAndroidDevice.State} {this.mAndroidDevice.ConnectMode} {this.mAndroidDevice.IsAndroid} to no device", "ConnectChanged");
                }
                else if (e.DeviceID == this.mAndroidDevice.DeviceID) // 同一个设备时
                {
                    this.mAndroidDevice = e;
                    MyLog.LogFile($"{this.mAndroidDevice.DeviceName} {this.mAndroidDevice.State} {this.mAndroidDevice.ConnectMode} {this.mAndroidDevice.IsAndroid} to same a device", "ConnectChanged");
                }
                else if (this.mAndroidDevice.HasUsbAuth == false && 
                         e.HasUsbAuth == true &&
                         e.IsAndroid == true &&
                         e.State == AirPlayState.Connected &&
                         this.mSelectDeviceMode != SelectDeviceMode.Activating &&
                         this.mSelectDeviceMode != SelectDeviceMode.Enable) // 从无授权切换到有usb权限的设备
                {
                    this.mAndroidDevice = e;
                    MyLog.LogFile($"{this.mAndroidDevice.DeviceName} {this.mAndroidDevice.State} {this.mAndroidDevice.ConnectMode} to a has usb auth device", "ConnectChanged");
                }
                else if (this.mSelectDeviceMode == SelectDeviceMode.Activating || this.mSelectDeviceMode == SelectDeviceMode.Deactivating) // 当处于do和开启监督解除监督模式时,不处理
                {
                    MyLog.LogFile($"{e.DeviceName} {e.State} {e.ConnectMode} to return because in Activating or Deactivating", "ConnectChanged");
                    return;
                }
                else // 已经连上设备然后又来一个设备时
                {
                    MyLog.LogFile($"{e.DeviceName} {e.State} {e.ConnectMode} {e.IsAndroid}to return", "ConnectChanged");
                    return;
                }
                
                switch (this.mAndroidDevice.State)
                {
                    case AirPlayState.Connected:
                        if (this.mAndroidDevice.IsAndroid) // android
                        {
                            if (this.mAndroidDevice.ConnectMode == ConnectMode.USB && !this.mAndroidDevice.DeviceName.Contains("error"))
                            {
                                this.InitPanel(this.pnlConnecting);
                                MyLog.LogFile($"{this.mAndroidDevice.DeviceName} {this.mAndroidDevice.ConnectMode} to Connecting in ConnectChanged", "ConnectChanged");
                            }
                            else
                            {
                                this.InitPanel(this.pnlAndroidConnectingGuide);
                                MyLog.LogFile($"{this.mAndroidDevice.DeviceName} {this.mAndroidDevice.ConnectMode} to Guide in ConnectChanged", "ConnectChanged");
                            }
                        }
                        else // ios
                        {
                            this.SelectFirstPage(); //选择首页

                            this.StartListenHandle();

                            if (!this.mCheckiTunesResult)
                                this.mTimer = TimerMgr.Create(0.5, this.InstalliTunesDriveHandle);
                        }
                        break;
                    case AirPlayState.Disconnected:
                        if (this.mAndroidDevice != null)
                        {
                            ThreadMgr.StartWithPara(SearchNextDevice, this.mAndroidDevice);
                        }
                        break;
                }
            }
        }

        /// <summary>DO权限回调事件</summary>
        private void MyUsb_DOCallback(object sender, DeviceOwnerCallback e)
        {
            MyLog.LogFile($"ErrorCode={e.code}", "ConnectChanged");

            string errorStr = this.Language.GetString("APC_RunTaskFail_Description");
            switch (e.code)
            {
                case -1:
                    // "DO权限设置失败\n原因：未知错误";
                    errorStr = this.Language.GetString("Parental_Connect_Failed_des"); // 抱歉，AirDroid Kids 强化监管模式开启失败，请重试！
                    break;
                case -2:
                    // "DO权限设置失败\n原因：手机上的账户没有删干净，这时候需要注销手机上的所有账户";
                    errorStr = this.Language.GetString("Parental_Connect_DOFailed_01"); // 请在“设置 - 账户”中退出手机当前所有的账号，并将应用双开、访客模式、多用户功能关闭后重试。
                    break;
                case -3:
                    // "DO权限设置失败\n原因：设备已经被设置了DO权限";
                    errorStr = this.Language.GetString("Parental_Connect_DOFailed_02"); // 强化监管模式需要获取Device Owner权限，请将设备中设置过Device Owner权限的应用卸载，或恢复出厂设置后重试。
                    break;
                case -4:
                    // "DO权限设置失败\n原因：系统正在移除该管理员权限，从而引发与已销毁对象交互的异常";
                    errorStr = this.Language.GetString("Parental_Connect_DOFailed_01"); // 请在“设置 - 账户”中退出手机当前所有的账号，并将应用双开、访客模式、多用户功能关闭后重试。
                    break;
                case -5:
                    // "DO权限设置失败\n原因：需要该权限的操作，却未被授予此权限时，开发者开启了 USB 调试(安全设置)";
                    errorStr = this.Language.GetString("Parental_Connect_DOFailed_03"); // 请到开发者选项里开启“USB调试（安全设置）”后重试。（MIUI系统设备开启后如果仍然开启失败，请关闭“MIUI优化”后重试）
                    break;
                case -6:
                    // "DO权限设置失败\n原因：请删除或关闭所有的多用户、访客模式以及应用双开";
                    errorStr = this.Language.GetString("Parental_Connect_DOFailed_01"); // 请在“设置 - 账户”中退出手机当前所有的账号，并将应用双开、访客模式、多用户功能关闭后重试。
                    break;
                case -7:
                    //errorStr = "DO权限设置失败\n原因：不支持设置DeviceOwner";
                    errorStr = this.Language.GetString("Parental_Connect_Failed_des"); // 抱歉，AirDroid Kids 强化监管模式开启失败，请重试！
                    break;
                case -8:
                    if (this.mAndroidDevice != null && this.mAndroidDevice.IsAndroid)
                    {
                        if (MyAirPlay.IsXiaoMiDevice(this.mAndroidDevice.HardwareId))
                        {
                            errorStr = this.Language.GetString("Parental_Connect_DOFailed_03");
                        }
                    }
                    break;
            }

            string guidStr = CreateDeviceGuid();
            MyLog.LogFile($"CreateDeviceGuid={guidStr}", "ConnectChanged");

            if (string.IsNullOrEmpty(this.mConnectorDeviceId))
            {
                this.mConnectorDeviceId = KidAPI.InitDevice(this.Language.CurrentLanguage.LangName, 1100);
            }

            KidAPI.EnabledDeviceOwnerResult(this.mConnectorDeviceId, guidStr, 0, e.code.ToString());
            if (this.mAndroidDevice != null)
                MyUsb.BroadCastMsgToDevice(this.mAndroidDevice.DeviceID, guidStr);
            this.BeginInvoke(new Action(() =>
            {
                this.InitPanel(this.pnlConnectionFailed, false);
                this.lblConnectionFailedTitle.skText = this.Language.GetString("Parental_Connect_supervisionmode_failedtoenable"); // 操作失败
                this.lblConnectionFailedNote.skText = errorStr;
                this.btnConnectionFailedBack.Visible = false;
            }));
        }

        /// <summary>检测是否有安装kid</summary>
        private void CheckInstallKid(object obj)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.AndroidDeviceNotKid);

            string btnText = this.Language.GetString("Parental_Connect_Usb_success_button_start");

            string device_id = (string)obj;
            if (string.IsNullOrEmpty(device_id))
                return;

            // 检查有无安装kid
            bool existResult = MyUsb.ExistApplication("com.sand.airdroidkids", device_id);
            if (existResult == false)
            {
                this.BeginInvoke(new Action(() =>
                {
                    this.BtnLoadingAnimation(this.btnStartEnableSupervisionMode, false, btnText, new Size(0, 0), skImageState.OneState, null);
                    string text = this.Language.GetString("Parental_Connect_NoKidsApp");
                    this.ShowInstallKidTip(text);
                }));
                return;
            }

            // 检查kid版本
            bool versionResult = MyUsb.CheckPackageVersion(this.mAndroidDevice.DeviceID, "com.sand.airdroidkids", "*******");
            if (versionResult == false)
            {
                this.BeginInvoke(new Action(() =>
                {
                    this.BtnLoadingAnimation(this.btnStartEnableSupervisionMode, false, btnText, new Size(0, 0), skImageState.OneState, null);
                    string text = this.Language.GetString("Parent_Kid_Version_Toolow");
                    this.ShowInstallKidTip(text);
                }));
                return;
            }

            // 检查线上配置
            bool checkOnlineManuResult = CheckOnlineUnspportDeviceSet(this.mAndroidDevice.Brand, this.mAndroidDevice.DeviceType, this.mAndroidDevice.OSBuildVersion);
            if (checkOnlineManuResult == false)
            {
                this.BeginInvoke(new Action(() =>
                {
                    this.BtnLoadingAnimation(this.btnStartEnableSupervisionMode, false, btnText, new Size(0, 0), skImageState.OneState, null);
                    string text = this.Language.GetString("Android_device_not_supported");
                    this.ShowInstallKidTip(text);
                }));
                return;
            }

            if (existResult && versionResult && checkOnlineManuResult)
            {
                this.ActivateSuperviseStart();
            }
        }

        /// <summary>android 开启开发者模式里面usb调试的步骤</summary>
        private void AndroidUsbTipChange(AndroidUsbTip tip)
        {
            switch (tip)
            {
                case AndroidUsbTip.One:
                    this.lblAndroidConnectingGuideTip1.skBackgroundImage = MyResource.GetImage("register_tips_1");
                    this.lblAndroidConnectingGuideTip2.skBackgroundImage = MyResource.GetImage("register_tips_2_dis");
                    this.lblAndroidConnectingGuideTip3.skBackgroundImage = MyResource.GetImage("register_tips_3_dis");
                    this.lblAndroidConnectingGuideTip4.skBackgroundImage = MyResource.GetImage("register_tips_4_dis");

                    this.lblAndroidConnectingGuideTipTitle.skText = this.Language.GetString("Parental_Connect_USBdebugging_01title"); // 激活【开发者选项】
                    this.lblAndroidConnectingGuideTipDesc.skText = this.Language.GetString("Parental_Connect_USBdebugging_01des"); // 依次点击设置>关于手机>点击7次内部版本号（在不同设备中也可能叫做“系统版本”。“安卓版本”、“编译版本”），开启开发者模式;

                    this.picAndroidConnectingGuideTip.Image = MyResource.GetImage("pic_guide_usb_1");
                    break;
                case AndroidUsbTip.Two:
                    this.lblAndroidConnectingGuideTip1.skBackgroundImage = MyResource.GetImage("register_tips_1_dis");
                    this.lblAndroidConnectingGuideTip2.skBackgroundImage = MyResource.GetImage("register_tips_2");
                    this.lblAndroidConnectingGuideTip3.skBackgroundImage = MyResource.GetImage("register_tips_3_dis");
                    this.lblAndroidConnectingGuideTip4.skBackgroundImage = MyResource.GetImage("register_tips_4_dis");

                    this.lblAndroidConnectingGuideTipTitle.skText = this.Language.GetString("Parental_Connect_USBdebugging_02title"); // 开启【USB调试】
                    this.lblAndroidConnectingGuideTipDesc.skText = this.Language.GetString("Parental_Connect_USBdebugging_02des"); // 进入设置>开发者选项，找到并开启USB调试

                    this.picAndroidConnectingGuideTip.Image = MyResource.GetImage("pic_guide_usb_2");
                    break;
                case AndroidUsbTip.Three:
                    this.lblAndroidConnectingGuideTip1.skBackgroundImage = MyResource.GetImage("register_tips_1_dis");
                    this.lblAndroidConnectingGuideTip2.skBackgroundImage = MyResource.GetImage("register_tips_2_dis");
                    this.lblAndroidConnectingGuideTip3.skBackgroundImage = MyResource.GetImage("register_tips_3");
                    this.lblAndroidConnectingGuideTip4.skBackgroundImage = MyResource.GetImage("register_tips_4_dis");

                    this.lblAndroidConnectingGuideTipTitle.skText = this.Language.GetString("Parental_Connect_USBdebugging_03title"); // 允许【USB调试】
                    this.lblAndroidConnectingGuideTipDesc.skText = this.Language.GetString("Parental_Connect_USBdebugging_03des"); // 如果安卓设备弹出“允许USB调试吗”窗口，勾选“一律允许使用这台计算机进行调试”，点击”允许“

                    this.picAndroidConnectingGuideTip.Image = MyResource.GetImage("pic_guide_usb_3");
                    break;
                case AndroidUsbTip.Four:
                    this.lblAndroidConnectingGuideTip1.skBackgroundImage = MyResource.GetImage("register_tips_1_dis");
                    this.lblAndroidConnectingGuideTip2.skBackgroundImage = MyResource.GetImage("register_tips_2_dis");
                    this.lblAndroidConnectingGuideTip3.skBackgroundImage = MyResource.GetImage("register_tips_3_dis");
                    this.lblAndroidConnectingGuideTip4.skBackgroundImage = MyResource.GetImage("register_tips_4");

                    this.lblAndroidConnectingGuideTipTitle.skText = this.Language.GetString("Parental_Connect_USBdebugging_04title"); // 选择 USB【文件传输】模式
                    this.lblAndroidConnectingGuideTipDesc.skText = this.Language.GetString("Parental_Connect_USBdebugging_04des"); // 下拉通知栏进入通知中心，找到 USB 选项，更改为【文件传输】模式

                    this.picAndroidConnectingGuideTip.Image = MyResource.GetImage("pic_guide_usb_4");
                    break;
            }

            CaculateUsbConnectingTipContentLocation();
            if (this.mAndroidTime != null) // 清除上次的定时器
            {
                this.mAndroidTime.Stop();
                this.mAndroidTime.Enabled = false;
                this.mAndroidTime.Dispose();
                this.mAndroidTime = null;
            }
        }

        /// <summary>重计算 usb 连接文本位置</summary>
        private void CaculateUsbConnectingTipContentLocation()
        {
            try
            {
                // 里面的14是标题与文本之间的固定间距
                int axis_x = this.lblAndroidConnectingGuideTipTitle.Location.X;
                int imgHeight = this.picAndroidConnectingGuideTip.Height;
                int textHeight = this.lblAndroidConnectingGuideTipTitle.Height + 14 + this.lblAndroidConnectingGuideTipDesc.Height;

                if (textHeight <= imgHeight)
                {
                    int subY = (imgHeight - textHeight) / 2;
                    int axis_y = this.picAndroidConnectingGuideTip.Top + subY;
                    this.lblAndroidConnectingGuideTipTitle.Location = new Point(axis_x, axis_y);
                    this.lblAndroidConnectingGuideTipDesc.Location = new Point(axis_x, this.lblAndroidConnectingGuideTipTitle.Bottom + 11);
                }
                else
                {
                    int subY = (textHeight - imgHeight) / 2;
                    int axis_y = this.picAndroidConnectingGuideTip.Top - subY;
                    this.lblAndroidConnectingGuideTipTitle.Location = new Point(axis_x, axis_y);
                    this.lblAndroidConnectingGuideTipDesc.Location = new Point(axis_x, this.lblAndroidConnectingGuideTipTitle.Bottom + 11);
                }
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "CaculateUsbConnectingTipContentLocation");
            }
        }

        /// <summary>android usb跳转去步骤一</summary>
        private void lblAndroidConnectingGuideTip1_Click(object sender, EventArgs e)
        {
            this.mAndroidUsbTip = AndroidUsbTip.One;
            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>android usb跳转去步骤二</summary>
        private void lblAndroidConnectingGuideTip2_Click(object sender, EventArgs e)
        {
            this.mAndroidUsbTip = AndroidUsbTip.Two;
            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>android usb跳转去步骤三</summary>
        private void lblAndroidConnectingGuideTip3_Click(object sender, EventArgs e)
        {
            this.mAndroidUsbTip = AndroidUsbTip.Three;
            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>android usb跳转去步骤四</summary>
        private void lblAndroidConnectingGuideTip4_Click(object sender, EventArgs e)
        {
            this.mAndroidUsbTip = AndroidUsbTip.Four;
            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>android usb连接下一个步骤</summary>
        private void btnAndroidConnectingGuideTipNext_Click(object sender, EventArgs e)
        {
            if (this.mAndroidUsbTip == AndroidUsbTip.One)
                this.mAndroidUsbTip = AndroidUsbTip.Two;
            else if (this.mAndroidUsbTip == AndroidUsbTip.Two)
                this.mAndroidUsbTip = AndroidUsbTip.Three;
            else if(this.mAndroidUsbTip == AndroidUsbTip.Three)
                this.mAndroidUsbTip = AndroidUsbTip.Four;
            else if(this.mAndroidUsbTip == AndroidUsbTip.Four)
                this.mAndroidUsbTip = AndroidUsbTip.One;

            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>android usb连接上一个步骤</summary>
        private void btnAndroidConnectingGuideTipBack_Click(object sender, EventArgs e)
        {
            if (this.mAndroidUsbTip == AndroidUsbTip.Four)
                this.mAndroidUsbTip = AndroidUsbTip.Three;
            else if (this.mAndroidUsbTip == AndroidUsbTip.Three)
                this.mAndroidUsbTip = AndroidUsbTip.Two;
            else if (this.mAndroidUsbTip == AndroidUsbTip.Two)
                this.mAndroidUsbTip = AndroidUsbTip.One;
            else if(this.mAndroidUsbTip == AndroidUsbTip.One)
                this.mAndroidUsbTip = AndroidUsbTip.Four;

            this.AndroidUsbTipChange(this.mAndroidUsbTip);
        }

        /// <summary>显示设备信息</summary>
        private void ShowDeviceMsg(skPanel panel)
        {
            int shouldHeight = 115;
            bool shouldVisible = true;

            if (this.mAndroidDevice == null)
                return;

            if (this.mAndroidDevice.IsAndroid) // android
            {
                shouldHeight = 85;
                shouldVisible = false;
            }
            else // ios
            {
                shouldHeight = 115;
                shouldVisible = true;
            }

            if(panel.Name == "pnlDisableConnectedSuccessDeviceMsg" || panel.Name == "pnlDeactivatingSupervisionModeDeviceMsg") // ios解除监督拿不到邮箱，所以跟android一样显示两行
            {
                shouldHeight = 85;
                shouldVisible = false;
            }

            skButton mailTip = null;
            skButton mail = null;
            switch (panel.Name)
            {
                case "pnlEnableConnectedSuccessDeviceMsg":
                    mailTip = panel.Controls["btnEnableConnectedSuccessDeviceMailTip"] as skButton;
                    mail = panel.Controls["btnEnableConnectedSuccessDeviceMail"] as skButton;
                    break;
                case "pnlActivatingSupervisionModeDeviceMsg":
                    mailTip = panel.Controls["btnActivatingSupervisionModeDeviceMailTip"] as skButton;
                    mail = panel.Controls["btnActivatingSupervisionModeDeviceMail"] as skButton;
                    break;
                case "pnlDisableConnectedSuccessDeviceMsg":
                    mailTip = panel.Controls["btnDisableConnectedSuccessDeviceMailTip"] as skButton;
                    mail = panel.Controls["btnDisableConnectedSuccessDeviceMail"] as skButton;
                    break;
                case "pnlDeactivatingSupervisionModeDeviceMsg":
                    mailTip = panel.Controls["btnDeactivatingSupervisionModeDeviceMailTip"] as skButton;
                    mail = panel.Controls["btnDeactivatingSupervisionModeDeviceMail"] as skButton;
                    break;
            }
            if (mailTip == null || mail == null)
                return;

            mailTip.Visible = shouldVisible;
            mail.Visible = shouldVisible;

            switch (panel.Name)
            {
                case "pnlEnableConnectedSuccessDeviceMsg":
                    this.btnEnableConnectedSuccessDevice.Visible = shouldVisible;
                    this.pnlEnableConnectedSuccessDeviceMsg.Height = shouldHeight;
                    this.lblEnableConnectedSuccessNotice.Location = new Point(this.lblEnableConnectedSuccessNotice.Location.X, this.pnlEnableConnectedSuccessDeviceMsg.Bottom + 12);
                    this.btnStartEnableSupervisionMode.Location = new Point(this.btnStartEnableSupervisionMode.Location.X, this.lblEnableConnectedSuccessNotice.Bottom + 60);
                    break;
                case "pnlActivatingSupervisionModeDeviceMsg":
                    this.pnlActivatingSupervisionModeDeviceMsg.Height = shouldHeight;
                    this.progressActivatingSupervisionMode.Location = new Point(this.progressActivatingSupervisionMode.Location.X, this.pnlActivatingSupervisionModeDeviceMsg.Bottom + 21);
                    this.lblActivatingSupervisionModeTip.Location = new Point(this.lblActivatingSupervisionModeTip.Location.X, this.progressActivatingSupervisionMode.Bottom + 13);
                    break;
                case "pnlDisableConnectedSuccessDeviceMsg":
                    this.pnlDisableConnectedSuccessDeviceMsg.Height = shouldHeight;
                    this.lblDisableConnectedSuccessNotice.Location = new Point(this.lblDisableConnectedSuccessNotice.Location.X, this.pnlDisableConnectedSuccessDeviceMsg.Bottom + 14);
                    this.btnStartDisableSupervisionMode.Location = new Point(this.btnStartDisableSupervisionMode.Location.X, this.lblDisableConnectedSuccessNotice.Bottom + 40);
                    break;
                case "pnlDeactivatingSupervisionModeDeviceMsg":
                    this.pnlDeactivatingSupervisionModeDeviceMsg.Height = shouldHeight;
                    this.progressDeactivatingSupervisionMode.Location = new Point(this.progressDeactivatingSupervisionMode.Location.X, this.pnlDeactivatingSupervisionModeDeviceMsg.Bottom + 21);
                    this.lblDeactivatingSupervisionModeTip.Location = new Point(this.lblDeactivatingSupervisionModeTip.Location.X, this.progressDeactivatingSupervisionMode.Bottom + 13);
                    break;
            }
        }

        /// <summary>usb连接视频链接</summary>
        private void lblAndroidConnectingGuideVideoLink_Click(object sender, EventArgs e)
        {
            Common.OpenUrl("https://help.airdroid.com/hc/sections/4404399385371");

            this.mActionHelper.AddKid(tdActionModeKeyForKid.AndroidUsbVideoGuide);
        }

        /// <summary>显示安装kid提示</summary>
        private void ShowInstallKidTip(string text)
        {
            int locationX = Math.Abs(this.Right - this.Left) / 2 + this.Left;
            int locationY = Math.Abs(this.Bottom - this.Top) / 2 + this.Top;
            frmInstallKidTip = new frmPopup("InstallKidTip", this.mActionHelper, locationX, locationY, text);
            frmInstallKidTip.Show();
        }

        /// <summary>
        /// 生成设备Guid
        /// </summary>
        private string CreateDeviceGuid()
        {
            string guidStr = string.Empty;
            try
            {
                Guid newGuid = Guid.NewGuid();
                guidStr = newGuid.ToString();
            }
            catch(Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateDeviceGuid");
            }
            return guidStr;
        }

        /// <summary>skButton开启或关闭Loading动画</summary>
        private void BtnLoadingAnimation(skButton btn, bool loading, string text, Size iconSize, skImageState imageState, Image image)
        {
            if (btn == null)
                return;

            btn.skText = text;
            btn.skIconSize = iconSize;
            btn.skIconState = imageState;
            btn.skIcon = image;

            if (loading)
            {
                btn.LoadGif();
                btn.Enabled = false;
            }
            else
            {
                btn.StopAnimate();
                btn.Enabled = true;
            }
        }

        /// <summary>检查下一个设备</summary>
        private void SearchNextDevice(object devObj)
        {
            AirPlayDevice dev = devObj as AirPlayDevice;
            if (dev == null)
                return;

            try
            {
                // 使用 FindIndex 提高查找效率
                int removeIndex = this.mAndroidDeviceList.FindIndex(d => string.Equals(dev.HardwareId, d.HardwareId));
                bool beforeDeviceIsIos = false;

                if (removeIndex >= 0)
                {
                    AirPlayDevice removeDev = this.mAndroidDeviceList[removeIndex];
                    beforeDeviceIsIos = !removeDev.IsAndroid; // 判定移除设备是否是IOS设备
                    this.mAndroidDeviceList.RemoveAt(removeIndex);
                    MyLog.LogFile($"removeDev={removeDev.DeviceName} {removeDev.ConnectMode} DeviceListCount={this.mAndroidDeviceList.Count} in SearchNextDevice", "ConnectChanged");
                }

                // 查找下一个可用设备
                AirPlayDevice nextDev = null;
                bool hasNext = false;

                lock (this.mAndroidDeviceList)
                {
                    nextDev = this.mAndroidDeviceList.Find(d => d.State == AirPlayState.Connected);
                    hasNext = nextDev != null;
                }

                if (hasNext)
                {
                    this.mAndroidDevice = nextDev;
                    if (!nextDev.IsAndroid && beforeDeviceIsIos)
                    {
                        this.DeviceConnectingHandle();
                    }
                    else
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            this.InitPanel(this.pnlFirstGuide);
                        }));

                        // 在后台线程中使用 Thread.Sleep 是安全的，不会阻塞UI
                        Thread.Sleep(500);
                        MyAirPlay_ConnectChanged(null, nextDev);
                        MyLog.LogFile($"nextDev={nextDev.DeviceName} {nextDev.ConnectMode} in SearchNextDevice", "ConnectChanged");
                    }
                }
                else
                {
                    this.mAndroidDevice = null;
                    MyLog.LogFile($"nextDev= null in SearchNextDevice", "ConnectChanged");
                    this.BeginInvoke(new Action(() =>
                    {
                        this.InitPanel(this.pnlFirstGuide);
                    }));
                }
            }
            catch(Exception ex)
            {
                Common.LogException(ex, "MainForm.SearchNextDevice");
            }
        }

        /// <summary>检查线上厂商配置</summary>
        private bool CheckOnlineUnspportDeviceSet(string brand, string model, string sdk_version)
        {
            // 提前返回，避免无效操作
            if (MyAirPlay.DicKidNmsSetting == null || MyAirPlay.DicKidNmsSetting.Count <= 0)
                return true;

            string unsupportDevicesStr = MyAirPlay.DicKidNmsSetting["unsupport_manufacturers"] as string;

            // 检查字符串是否为空或null
            if (string.IsNullOrEmpty(unsupportDevicesStr))
                return true;

            // 统一处理输入参数
            brand = brand.ToLower().Trim();
            model = model.ToLower().Trim();
            sdk_version = sdk_version.ToLower().Trim();
            unsupportDevicesStr = unsupportDevicesStr.ToLower().Trim(); // 增加Trim以保证一致性

            // 转换为设备列表
            List<KidNmsUnSpportSet> unspportDevices = KidNmsUnSpportSet.ConvertToList(unsupportDevicesStr);
            if (unspportDevices == null)
                return true;

            // 遍历设备列表进行匹配
            foreach (KidNmsUnSpportSet device in unspportDevices)
            {
                // 使用插值语法优化日志输出
                MyLog.LogFile($"device.brand={device.brand} device.model={device.model} device.sdk_version={device.sdk_version} yours:{brand} {model} {sdk_version}", "ConnectChanged");

                // 合并条件判断，减少冗余
                if ((string.Equals(device.brand, brand) && string.Equals(device.model, model) && string.Equals(device.sdk_version, sdk_version)) ||
                    (string.Equals(device.brand, brand) && string.Equals(device.model, model)) ||
                    (string.Equals(device.brand, brand) && string.Equals(device.sdk_version, sdk_version)))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 设置桌面图标
        /// </summary>
        private void SetDesktopIcon()
        {
            string appPath = Path.Combine(Folder.AppFolder, "AirDroidParentalConnector.exe");

            // 前置校验 Folder.AppFolder 是否有效
            if (string.IsNullOrWhiteSpace(Folder.AppFolder))
            {
                Common.LogException(new InvalidOperationException("AppFolder is null or empty."), "MainForm.SetDesktopIcon");
                return;
            }

            string[] desktopPaths = {
                Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory),
                Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
            };

            foreach (string desktopPath in desktopPaths)
            {
                try
                {
                    DesktopLink.ModifyShutcutTargetPath("AirDroid Parental Connector", appPath, desktopPath);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, $"MainForm.SetDesktopIcon - ModifyShutcutTargetPath failed for {desktopPath}");
                }

                try
                {
                    DesktopLink.ModifyShutcutOnly("AirDroid Parental Connector", "AirDroidParentalConnector.exe", desktopPath);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, $"MainForm.SetDesktopIcon - ModifyShutcutOnly failed for {desktopPath}");
                }
            }
        }

        /// <summary>反馈窗口关闭</summary>
        private void frmFeedback_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.frmFeedback.FormClosed -= frmFeedback_FormClosed;
            this.frmFeedback = null;
        }
#endregion

    }
}
