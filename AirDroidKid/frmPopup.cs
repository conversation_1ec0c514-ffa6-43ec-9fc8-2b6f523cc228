﻿using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Net.NetworkInformation;
using System.Text;
using System.Windows.Forms;

namespace AirDroidKid
{
    public partial class frmPopup : frmBase
    {

        public override skSplit skTransparentImageSplit => new skSplit(15);

        private string mWindowType = string.Empty;

        private int mCountDownSeconds = 9;

        private string mEmail = string.Empty;

        private string mDeviceName = string.Empty;

        public string Email { get; private set; }

        private string mPairingCode = string.Empty;

        public string PairingCode { get; private set; }

        public string InstallKidShowText = string.Empty;

        public frmPopup mCustomError = null;

        public KidProfileInfo KidProfileInfo { get; private set; }

        private Color mErrorTipColor = Color.FromArgb(225, 62, 62);

        private Color mNormalTipColor = Color.FromArgb(228, 230, 234);

        private string mOKstr = string.Empty;

        private tdActionHelper<tdActionItemForKid> mActionHelper = null;

        private skTimer mTimer = null;

        public frmPopup(string windowType, tdActionHelper<tdActionItemForKid> actionHelper, string deviceName = null, string email = null, string pairingCode = null)
        {
            InitializeComponent();

            this.mWindowType = windowType;

            this.mEmail = email;

            this.mActionHelper = actionHelper;

            this.mPairingCode = pairingCode;

            this.mDeviceName = deviceName;
        }

        public frmPopup(string windowType, tdActionHelper<tdActionItemForKid> actionHelper, int locationX, int locationY, string installKidShowText)
        {
            InitializeComponent();

            this.mWindowType = windowType;
            this.mActionHelper = actionHelper;
            this.StartPosition = FormStartPosition.Manual;
            this.Location = new Point(locationX, locationY);
            this.InstallKidShowText = installKidShowText;
        }

        #region public系列方法
        /// <summary>
        /// 设置自定义错误信息
        /// </summary>
        public void SetCustomError(CustomInfo customInfo)
        {
            this.lblCustomErrorTitle.skText = customInfo.title;
            this.lblCustomErrorDesc.skText = customInfo.body;

            this.flpCustomError.Controls.Clear();

            foreach (ButtonInfo item in customInfo.buttons)
            {
                skButton button = this.CreateErrorButton();
                button.skText = item.title;
                string eventName = <EMAIL>();
                switch (eventName)
                {
                    case "close":
                    case "back":
                        button.Click += btnCustomErrorCloseType;
                        break;
                    case "redirect":
                        button.Click += (sender, e) =>
                        {
                            Common.OpenUrl(<EMAIL>);
                            this.DialogResult = DialogResult.OK;
                        };
                        break;
                }
                this.flpCustomError.Controls.Add(button);
            }
        }
        #endregion

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.InitStyle();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PopupForm.InitControls");
            }
        }

        private void InitStyle()
        {
            this.btnClose.skBackgroundImage = Properties.Resources.btn_close_4;
            this.btnClose.skBackgroundImageState = skImageState.FourState;
            this.btnClose.skShowIcon = false;

            this.skShowTitle = false;

            this.skTitle = null;
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
            this.skTitleColor = Color.FromArgb(45, 47, 51);

            this.pnlEnterPairingCode.Controls.Clear();
            this.pnlConfirm.Controls.Clear();
            this.pnlAccountExpired.Controls.Clear();

            string cancelStr = this.Language.GetString("Common_Cancel"); // 取消

            switch (mWindowType)
            {
                case "PairingCode":

                    this.lblPairingCodeNotExistTip.skText = this.Language.GetString("Parental_Connect_code_error1");  //配对码不存在或已失效

                    this.lblPairingCodeTip.SetText(this.Language.GetString("APC_PairingCode_More_Tip"), "link"); //您可以在 AirDroid Parental Control 或 AirDroid Kids iOS 的监督模式开启引导页面中找到配对码。<Link=more>了解更多</Link>
                    List<skItemInfo> skItemInfos = this.lblPairingCodeTip.GetItemList("link");
                    skItemInfos[0].LinkUrl = "https://help.airdroid.com/hc/articles/**************";

                    this.lblEnterPairingCode.skText = this.Language.GetString("Parental_Connect_code_title1"); //请输入设备上的配对码

                    int pairingCodePanelHeight = this.lblEnterPairingCode.Height + this.txtEnterPairingCode1.Height + this.lblPairingCodeNotExistTip.Height + this.lblPairingCodeTip.Height + 82;

                    this.pnlEnterPairingCode.Size = new Size(380, pairingCodePanelHeight);

                    this.Size = new Size(380, pairingCodePanelHeight);

                    this.CenterDisplay();

                    this.pnlEnterPairingCode.Location = new Point(1, 28);

                    this.pnlEnterPairingCode.skBorderRadius = new skBorderRadius(5);

                    this.pnlEnterPairingCode.Controls.AddRange(
                        new Control[]
                        {
                            this.lblEnterPairingCode,
                            this.lblEnterPairingCode1,
                            this.lblEnterPairingCode2,
                            this.lblEnterPairingCode3,
                            this.lblEnterPairingCode4,
                            this.lblEnterPairingCode5,
                            this.lblEnterPairingCode6,
                            this.txtEnterPairingCode1,
                            this.txtEnterPairingCode2,
                            this.txtEnterPairingCode3,
                            this.txtEnterPairingCode4,
                            this.txtEnterPairingCode5,
                            this.txtEnterPairingCode6,
                            this.lblPairingCodeNotExistTip,
                            this.lblPairingCodeTip,
                            this.lblEnterPairingCodeLine,
                            this.picLoading,
                        });

                    this.txtEnterPairingCode1.MaxLength = 1;
                    this.txtEnterPairingCode2.MaxLength = 1;
                    this.txtEnterPairingCode3.MaxLength = 1;
                    this.txtEnterPairingCode4.MaxLength = 1;
                    this.txtEnterPairingCode5.MaxLength = 1;
                    this.txtEnterPairingCode6.MaxLength = 1;

                    this.pnlEnterPairingCode.BringToFront();

                    this.TimerStop();
                    this.mTimer = TimerMgr.Create(0.5, SetskTextBoxHandle, "SetskTextBoxTimer", 1); //为了解决配对码弹窗 skTextBox闪动问题

                    break;

                case "Confirm":

                    this.skTitle = "Confirm";

                    this.lblConfirmEmail.skText = this.mEmail;

                    this.lblConfirmTitle.skText = string.Format(this.Language.GetString("Parental_Connect_code_title2"), this.mDeviceName); //请确认当前设备({0})绑定于以下账号：

                    this.lblConfirmDescribe.skText = this.Language.GetString("Parental_Connect_code_text1"); // 监督模式开启成功后，此账号可以通过家长端对此设备的应用、网站、屏幕时间、系统设置等多项内容进行管控。

                    mOKstr = this.Language.GetString("Common_OK"); // 好的;
                    this.btnConfirmOK.skText = this.btnConfirmOK.skText = $"{this.mOKstr}(10s)";

                    this.btnConfirmCancel.skText = cancelStr;//Cancel

                    int confirmPanelHeight = this.lblConfirmTitle.Height + this.lblConfirmEmail.Height + lblConfirmDescribe.Height + btnConfirmOK.Height + 63;
                    
                    this.Size = new Size(480, confirmPanelHeight);

                    this.CenterDisplay();

                    this.lblConfirmEmail.Location = new Point(24, this.lblConfirmTitle.Location.Y + this.lblConfirmTitle.Height + 5);

                    this.lblConfirmDescribe.Location = new Point(24, this.lblConfirmEmail.Location.Y + this.lblConfirmEmail.Height + 5);

                    int btnY = this.lblConfirmDescribe.Location.Y + this.lblConfirmDescribe.Height + 10;

                    this.btnConfirmOK.Padding = new Padding(0, 2, 0, 0);
                    this.btnConfirmCancel.Padding = new Padding(0, 2, 0, 0);

                    this.btnConfirmOK.Size = new Size(90, 28);
                    this.btnConfirmCancel.Size = new Size(90, 28);

                    this.btnConfirmCancel.Location = new Point(this.pnlConfirm.Width - 20 - this.btnConfirmCancel.Width, btnY);
                    this.btnConfirmOK.Location = new Point(this.btnConfirmCancel.Location.X - 20 - this.btnConfirmOK.Width, btnY);

                    this.btnConfirmCancel.skBorderRadius = new skBorderRadius(14);
                    this.btnConfirmOK.skBorderRadius = new skBorderRadius(14);

                    this.pnlConfirm.Size = new Size(482, confirmPanelHeight);
                    this.pnlConfirm.Location = new Point(1, 28);

                    this.pnlConfirm.Controls.AddRange(
                        new Control[]
                        {
                            this.lblConfirmTitle,
                            this.lblConfirmEmail,
                            this.lblConfirmDescribe,
                            this.btnConfirmOK,
                            this.btnConfirmCancel
                        });

                    this.pnlConfirm.BringToFront();

                    this.TimerStop();
                    this.mTimer = TimerMgr.Create(0.01, this.BtnConfirmCountDownHandle, "BtnConfirmCountDownTimer", 1); //为了解决确认弹窗 确定按钮先于其他控件显示问题

                    break;

                case "InstallKidTip":
                    this.Size = this.pnlInstallKid.Size;
                    this.Location = new Point(this.Left - this.Width / 2, this.Top - this.Height / 2);
                    this.pnlInstallKid.Location = new Point(1, 28);
                    this.pnlInstallKid.BringToFront();

                    this.lblInstallKidDesc.skText = this.InstallKidShowText; // 检测到此应用还未安装 AirDroid Kids，请先安装 AirDroid Kids 并绑定后继续。
                    this.lblInstallKidHelpLink.skText = this.Language.GetString("Parental_Connect_JumpTo_Guide"); // 查看使用指南
                    this.btnInstallKidOK.skText = this.Language.GetString("Common_gotit_tip"); // 我知道了
                    break;

                case "ShowCustomError":
                    this.Size = this.pnlCustomError.Size;
                    this.pnlCustomError.Location = new Point(1, 28);
                    this.pnlCustomError.BringToFront();
                    break;

                default:
                    break;
            }

            this.lblAccountExpiredTitle.skText = this.Language.GetString("err_account_expire"); //账号已过期
            this.lblAccountExpiredContent.skText = this.Language.GetString("APC_AccountExpiration_Tip"); //当前账号: {0} 已过期，请购买后重新执行开启监督模式操作。
            this.btnBuyNow.skText = this.Language.GetString("rs_purchase_button"); //立即购买
            this.btnBuyCancel.skText = cancelStr;

            this.skBorderRadius = new skBorderRadius(2);
            this.skTransparentImage = Properties.Resources.frm_bg_shadow;
            this.skTransparentImagePadding = new Padding(6, 6, 6, 6);
            this.skBorderStrokeColor = Color.White;
        }

        private void TimerStop()
        {
            if (this.mTimer != null)
            {
                this.mTimer.Dispose();
                this.mTimer = null;
            }
        }

        private delegate void SetskTextBoxHandler();
        /// <summary>显示配对码skTextBox</summary>
        private void SetskTextBoxHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new SetskTextBoxHandler(SetskTextBoxHandle));
                return;
            }

            this.txtEnterPairingCode1.BringToFront();
            this.txtEnterPairingCode2.BringToFront();
            this.txtEnterPairingCode3.BringToFront();
            this.txtEnterPairingCode4.BringToFront();
            this.txtEnterPairingCode5.BringToFront();
            this.txtEnterPairingCode6.BringToFront();

            this.txtEnterPairingCode1.Visible = true;
            this.txtEnterPairingCode2.Visible = true;
            this.txtEnterPairingCode3.Visible = true;
            this.txtEnterPairingCode4.Visible = true;
            this.txtEnterPairingCode5.Visible = true;
            this.txtEnterPairingCode6.Visible = true;

            this.txtEnterPairingCode1.Focus();
        }

        /// <summary>居中显示</summary>
        private void CenterDisplay()
        {
            if (this.Owner != null)
            {
                MyForm.SetParentCenter(this, this.Owner);

                this.Location = new Point(this.Location.X, this.Location.Y - 60);
            }
        }

        /// <summary>
        /// 创建自定义错误按钮
        /// </summary>
        private skButton CreateErrorButton()
        {
            skButton button = new skButton();
            button.MaximumSize = new Size(430, 28);
            button.Padding = new Padding(12, 4, 12, 4);
            button.skBackgroundColor = Color.White;
            button.Cursor = Cursors.Hand;
            button.skAdriftIconWhenHover = false;
            button.skAutoHeight = false;
            button.skAutoSize = true;
            button.skAutoSizeByBackgroudImage = false;
            button.skBackgroundImage = MyResource.GetImage("btn_4_sure.png");
            button.skBackgroundImageState = skImageState.FourState;
            button.skBackgroundImageStretch = false;
            button.skBadgeBackgroundDiff = 3;
            button.skBadgeBackgroundRaduis = new skBorderRadius(5, 5, 5, 5);
            button.skTextColor = Color.White;
            return button;
        }

        private delegate void BtnConfirmCountDownHandler();
        /// <summary>确认按钮倒计时</summary>
        private void BtnConfirmCountDownHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new BtnConfirmCountDownHandler(BtnConfirmCountDownHandle));
                return;
            }

            this.btnConfirmOK.skTextColorDisable = Color.White;

            this.mCountDownSeconds = 9;

            this.timer_BtnConfirmTimeCount.Interval = 1000;

            this.timer_BtnConfirmTimeCount.Start();
        }

        private Color mTextPressColor = Color.FromArgb(40, 41, 48);
        private void txtEnterPairingCode1_KeyPress(object sender, KeyPressEventArgs e)
        {
            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode1, this.txtEnterPairingCode1, this.txtEnterPairingCode2, this.lblEnterPairingCode1);
        }

        private void txtEnterPairingCode2_KeyPress(object sender, KeyPressEventArgs e)
        {
            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode1, this.txtEnterPairingCode2, this.txtEnterPairingCode3, this.lblEnterPairingCode2);
        }

        private void txtEnterPairingCode3_KeyPress(object sender, KeyPressEventArgs e)
        {
            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode2, this.txtEnterPairingCode3, this.txtEnterPairingCode4, this.lblEnterPairingCode3);
        }

        private void txtEnterPairingCode4_KeyPress(object sender, KeyPressEventArgs e)
        {
            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode3, this.txtEnterPairingCode4, this.txtEnterPairingCode5, this.lblEnterPairingCode4);
        }

        private void txtEnterPairingCode5_KeyPress(object sender, KeyPressEventArgs e)
        {
            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode4, this.txtEnterPairingCode5, this.txtEnterPairingCode6, this.lblEnterPairingCode5);
        }

        private void txtEnterPairingCode6_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
                e.Handled = true;

            this.KeyPressAction(e.KeyChar, this.txtEnterPairingCode5, this.txtEnterPairingCode6, this.txtEnterPairingCode6, this.lblEnterPairingCode6);
        }

        private void KeyPressAction(char keyChar, skTextBox frontTB, skTextBox currentTB, skTextBox nextTB, skLabel currentLbl)
        {
            if (keyChar == (char)Keys.Back)
            {
                if (string.IsNullOrEmpty(currentTB.Text))
                {
                    frontTB.Text = string.Empty;
                    frontTB.Focus();
                }
                else
                {
                    this.ReplyToNormalState(currentLbl);
                    currentTB.Focus();
                }
            }
            else
            {
                if (!IsNumber(keyChar))
                    return;

                nextTB.Focus();

                currentTB.ForeColor = mTextPressColor;

                if (currentTB == this.txtEnterPairingCode6)
                    currentTB.TextChanged += new EventHandler(this.txtEnterPairingCode6_TextChanged);
            }
        }

        private bool IsNumber(char keyChar)
        {
            bool result = false;

            if (keyChar >= 65296 && keyChar <= 65305 || keyChar >= (char)Keys.D0 && keyChar <= (char)Keys.D9)
                result = true;

            return result;
        }

        private void ReplyToNormalState(skLabel label)
        {
            this.SetLableColor(false);

            this.lblPairingCodeNotExistTip.Visible = false;

            if (this.Height > 229)
                this.Height -= 10;
        }

        private void CheckPairingCode()
        {
            string strCode = string.Concat(this.txtEnterPairingCode1.Text.Replace(" ", ""), this.txtEnterPairingCode2.Text.Replace(" ", ""),
                this.txtEnterPairingCode3.Text.Replace(" ", ""), this.txtEnterPairingCode4.Text.Replace(" ", ""),
                this.txtEnterPairingCode5.Text.Replace(" ", ""), this.txtEnterPairingCode6.Text.Replace(" ", ""));

            if (string.IsNullOrEmpty(strCode))
                return;

            this.mPairingCode = strCode;

            this.SetControlState(true);

            ThreadMgr.Start(() =>
            {
                this.ThreadCheckPairingCode(strCode);

            });
        }

        private CustomInfo mCustomInfo = null;
        private KidProfileInfo mKidPorfileInfo = null;

        private void ThreadCheckPairingCode(string strCode)
        {
            bool result = false;
            ErrorType errorType = ErrorType.Code;
            try
            {
                if (NetworkInterface.GetIsNetworkAvailable())
                {
                    ServerCustomArgs<KidProfileInfo> args = null;

                    this.mCustomInfo = null;

                    if (!string.IsNullOrEmpty(strCode) && strCode.Length == 6)
                    {
                        strCode = strCode.Normalize(NormalizationForm.FormKC);

                        args = KidAPI.GetProfile(strCode);

                        if (args != null && args.Code == 1)
                            result = true;

                        if (args.Code == -99999) //自定义错误
                        {
                            this.mCustomInfo = args.custom_info;
                            errorType = ErrorType.Custom;
                        }

                        if (args.Code == -2000901)  //不是vip用户
                        {
                            this.mKidPorfileInfo = args.Data;
                            errorType = ErrorType.NotVip;
                        }
                    }

                    if (result)
                    {
                        Email = args.Data.mail;

                        KidProfileInfo = args.Data;

                        PairingCode = this.mPairingCode;
                    }
                }
                else
                {
                    errorType = ErrorType.Net;
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ThreadCheckPairingCode");
            }
            finally
            {
                if (result)
                {
                    this.DialogResult = DialogResult.Yes;
                }
                else
                {
                    this.SetControlState(false);

                    this.SetErrorState(true, errorType);
                }
            }
        }

        private delegate void SetErrorStateHandler(bool isError, ErrorType errorType = ErrorType.Code);
        private void SetErrorState(bool isError, ErrorType errorType = ErrorType.Code)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetErrorStateHandler(SetErrorState), isError, errorType);
                return;
            }

            if (isError)
            {
                this.txtEnterPairingCode6.TextChanged -= this.txtEnterPairingCode6_TextChanged;

                this.txtEnterPairingCode6.Focus();

                if (errorType == ErrorType.NotVip)
                {
                    this.lblAccountExpiredContent.skText = string.Format(this.Language.GetString("APC_AccountExpiration_Tip"), mKidPorfileInfo.mail); //当前账号: {0} 已过期，请购买后重新执行开启监督模式操作。

                    this.btnBuyCancel.Location = new Point(480 - 20 - this.btnBuyCancel.Width, this.lblAccountExpiredContent.Location.Y + this.lblAccountExpiredContent.Height + 18);
                    this.btnBuyNow.Location = new Point(this.btnBuyCancel.Location.X - 15 - this.btnBuyNow.Width, this.btnBuyCancel.Location.Y);

                    int panelHeight = this.lblAccountExpiredTitle.Height + this.lblAccountExpiredContent.Height + this.btnBuyNow.Height + 60;
                    this.Size = new Size(480, panelHeight + 28);

                    this.pnlAccountExpired.Size = new Size(480, panelHeight);
                    this.pnlAccountExpired.Location = new Point(1, 28);

                    this.pnlAccountExpired.Controls.AddRange(
                        new Control[]
                        {
                            this.lblAccountExpiredTitle,
                            this.lblAccountExpiredContent,
                            this.btnBuyNow,
                            this.btnBuyCancel,
                        });
                    this.pnlAccountExpired.BringToFront();

                    return;
                }

                if (errorType == ErrorType.Custom)
                {
                    if(mCustomError == null)
                    {
                        mCustomError = new frmPopup("ShowCustomError", this.mActionHelper, this.Location.X, this.Location.Y, this.mCustomInfo.buttons[0].title);
                        mCustomError.SetCustomError(this.mCustomInfo);
                        mCustomError.ShowDialog(this);
                        mCustomError.Close();
                        mCustomError = null;
                    }

                    //UserBase.ShowCustomInfo(this.mCustomInfo, this, null, null);
                    return;
                }

                if (errorType == ErrorType.Net)
                    this.lblPairingCodeNotExistTip.skText = this.Language.GetString("rs_discount_error_tip"); //网络异常，请检查网络后重试

                if (this.Height < 221)
                    this.Height += 10;
            }

            if (errorType == ErrorType.Code)
            {
                this.SetLableColor(isError);
                this.lblPairingCodeNotExistTip.skText = this.Language.GetString("Parental_Connect_code_error1");  //配对码不存在或已失效
            }

            this.lblPairingCodeNotExistTip.Visible = isError;
        }

        private void SetLableColor(bool isError)
        {
            Color boderStrokeColor = isError ? mErrorTipColor : mNormalTipColor;

            this.lblEnterPairingCode1.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode2.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode3.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode4.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode5.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode6.skBorderStrokeColor = boderStrokeColor;
        }

        private enum ErrorType
        {
            Net,
            Code,
            Custom,
            NotVip
        }

        private delegate void SetControlStateHandler(bool isLoading);
        /// <summary></summary>
        private void SetControlState(bool isLoading)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetControlStateHandler(SetControlState), isLoading);
                return;
            }

            this.picLoading.Visible = isLoading;

            this.lblEnterPairingCode.Enabled = !isLoading;

            this.txtEnterPairingCode1.Enabled = !isLoading;
            this.txtEnterPairingCode2.Enabled = !isLoading;
            this.txtEnterPairingCode3.Enabled = !isLoading;
            this.txtEnterPairingCode4.Enabled = !isLoading;
            this.txtEnterPairingCode5.Enabled = !isLoading;
            this.txtEnterPairingCode6.Enabled = !isLoading;
        }

        private void btnConfirmOK_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.PairingCodeConfirmOK);

            this.DialogResult = DialogResult.OK;
        }

        private void btnConfirmCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnEnter_Click(object sender, EventArgs e)
        {
            this.CheckPairingCode();
        }

        private void timer_BtnConfirmTimeCount_Tick(object sender, EventArgs e)
        {
            if (this.mCountDownSeconds != 0)
                this.btnConfirmOK.skText = $"{this.mOKstr}({this.mCountDownSeconds}s)";

            if (this.mCountDownSeconds > 0)
            {
                this.mCountDownSeconds--;
            }
            else
            {
                this.timer_BtnConfirmTimeCount.Stop();

                this.btnConfirmOK.skText = mOKstr;

                this.btnConfirmOK.Enabled = true;
            }
        }

        private void txtEnterPairingCode6_TextChanged(object sender, EventArgs e)
        {
            this.CheckPairingCode();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            this.txtEnterPairingCode1.Focus();
        }

        private void btnBuyNow_Click(object sender, EventArgs e)
        {
            Common.OpenUrl("https://www.airdroid.com/pricing/airdroid-parental-control/");
        }

        private void btnBuyCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void lblPairingCodeTip_Click(object sender, EventArgs e)
        {
            skLabel skLabel = sender as skLabel;

            if (skLabel.Cursor == Cursors.Hand)
                this.mActionHelper.AddKid(tdActionModeKeyForKid.PairingCodeLearnMore);
        }

        /// <summary>查看使用指南链接</summary>
        private void lblInstallKidHelpLink_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.AndroidDeviceNotKidGuide);

            string lang = this.Language.CurrentLanguage.LangWithoutRegion;

            if (lang.Contains("zh-cn"))
                Common.OpenUrl("https://www.airdroid.cn/guide/parental-control/");
            else
                Common.OpenUrl("https://www.airdroid.com/guide/parental-control/");

            this.Close();
        }

        /// <summary>安装kid点击OK</summary>
        private void btnInstallKidOK_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddKid(tdActionModeKeyForKid.PairingCodeConfirmOK);

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>自定义错误关闭类型</summary>
        private void btnCustomErrorCloseType(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}
