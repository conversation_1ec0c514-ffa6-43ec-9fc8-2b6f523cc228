﻿using System.Diagnostics;
using System;
using System.Collections;
using System.Data;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Data.SQLite3;

using iTong.CoreFoundation;

namespace iTong.Device
{
    public class mbdb
    {
        #region DllImport

        const string DllPath = "BackupDecrypt.dll";

        [UnmanagedFunctionPointer(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        private delegate void LogCallback(IntPtr hUserData, string target, string msg);

        [DllImport(DllPath, EntryPoint = "Log", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern void DecryptLog(LogCallback hUserLog, IntPtr hUserData);

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern long CreateDecrypt();

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern bool InitWithManifest(long uid, string password, string filePath);

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern bool DecryptDB(long uid, string inDbPath, string outDbPath);

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern bool DecryptFileWithDBBlob(long uid, IntPtr hPlistData, int sizePlist, string inFilePath, string outFilePath);

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        private static extern void DestroyDecrypt(long uid);

        #endregion

        #region Decrypt

        private static object mLockerDecrypt = new object();
        private static long mCurrentUID = 0;
        private static string mCurrentBackupDir = string.Empty;

        public static void OnLogCallback(IntPtr hUserData, string target, string msg)
        {
            LogMbdb(string.Format("{0} -> {1}", target, msg));
        }

        public static void LogMbdb(string msg)
        {
            Common.Log(msg, "mbdb", true);
        }

        static mbdb()
        {
            try
            {
                string dllPath = Path.Combine(Folder.AppFolder, DllPath);
                if (File.Exists(dllPath))
                {
                    DecryptLog(new LogCallback(OnLogCallback), IntPtr.Zero);
                }
            }
            catch (Exception ex)
            {
                LogMbdb(ex.ToString());
            }
        }
      
        public static MBFileSearchResult FilterDataFromBackup(string dirBackup, string password, MBFileSearch keyWord)
        {
            MBFileSearchResult result = new MBFileSearchResult();

            SQLiteConnection conn = null;

            string dirDecrypt = string.Empty;
            string dirUDID = string.Empty;

            lock (mLockerDecrypt)
            {
                try
                {
                    dirBackup = dirBackup.TrimEnd(Path.DirectorySeparatorChar);
                    string ManifestPlist = Path.Combine(dirBackup, "Manifest.plist");
                    if (!File.Exists(ManifestPlist))
                    {
                        result.Type = MBErrorType.ManifestPlistNotExist;
                        result.ErrMsgAppendLine(string.Format("File is not exist. {0}", ManifestPlist));
                        goto DoExit;
                    }

                    string ManifestDB = Path.Combine(dirBackup, "Manifest.db");
                    string ManifestDB_Decrypt = ManifestDB;

                    if (!File.Exists(ManifestDB))
                    {
                        result.Type = MBErrorType.ManifestDbNotExist;
                        result.ErrMsgAppendLine(string.Format("File is not exist. {0}", ManifestDB));
                        goto DoExit;
                    }

                    iPhoneDevice dev = GetDeviceFromManifestPlist(dirBackup);

                    if (dev.IsEncrypted)
                    {
                        dirDecrypt = Path.Combine(Folder.ApplicationDataFolder, "iTunesDecrypt");
                        dirUDID = Path.Combine(dirDecrypt, dev.UniqueDeviceID);

                        Folder.CheckFolder(dirUDID);

                        if (dirBackup != mCurrentBackupDir || mCurrentUID <= 0)
                        {
                            if (mCurrentUID > 0)
                            {
                                //释放上次解密缓存数据
                                DestroyDecrypt(mCurrentUID);
                            }

                            //创建新的解密缓存数据                            
                            mCurrentUID = CreateDecrypt();

                            //根据Manifest.plist中的KegBag和iTunes备份密码，初始化解密密钥
                            if (!InitWithManifest(mCurrentUID, password, ManifestPlist))
                            {
                                result.Type = MBErrorType.ManifestDbDecryptError;
                                result.ErrMsgAppendLine(string.Format("InitWithManifest failed. \r\nManifestPlist = {0}", ManifestPlist));
                                goto DoExit;
                            }
                        }

                        //使用Manifest.db文件的最后修改时间，作为是否重新解密Manifest.db
                        FileInfo dbInfo = new FileInfo(ManifestDB);
                        string dbName = string.Format("{0}_{1}_Manifest.db", dbInfo.LastWriteTime.ToString("yyyyMMdd_HHmmss"), dbInfo.Length.ToString().PadLeft(12, '0'));

                        ManifestDB_Decrypt = Path.Combine(dirUDID, dbName);
                        if (!File.Exists(ManifestDB_Decrypt))
                        {
                            //解密Manifest.db
                            if (!DecryptDB(mCurrentUID, ManifestDB, ManifestDB_Decrypt))
                            {
                                Utility.DeleteFile(ManifestDB_Decrypt);

                                result.ErrMsgAppendLine(string.Format("DecryptDB failed. \r\n ManifestDB = {0}, ManifestDB_Decrypt = {1}", ManifestDB, ManifestDB_Decrypt));
                                goto DoExit;
                            }
                        }

                        //解密Manifest.db成功，再更新当前备份缓存的目录
                        mCurrentBackupDir = dirBackup;
                    }

                    conn = SQLiteClass3.CreateConnectionFromFile(ManifestDB_Decrypt);
                    if (conn == null)
                    {
                        result.Type = MBErrorType.ManifestDbConnectFailed;
                        result.ErrMsgAppendLine(string.Format("CreateConnectionFromFile failed. {0}", ManifestDB_Decrypt));
                        goto DoExit;
                    }


                    StringBuilder sbWhere = new StringBuilder();
                    sbWhere.Append(" where 1=1 ");

                    if (keyWord.Domain != MBFileDomainType.None)
                    {
                        sbWhere.Append(string.Format(" and domain = '{0}'", MBFileRecord.GetDomainValue(keyWord.Domain)));
                    }

                    if (!string.IsNullOrEmpty(keyWord.FileID))
                    {
                        sbWhere.Append(string.Format(" and fileID = '{0}'", keyWord.FileID));
                    }

                    StringBuilder sbPath = new StringBuilder();
                    if (!string.IsNullOrEmpty(keyWord.RelativePath))
                        sbPath.Append(string.Format(" relativePath = '{0}'", SQLiteClass3.ReplaceSqlStr(keyWord.RelativePath)));

                    if (!string.IsNullOrEmpty(keyWord.LikePath))
                    {
                        if (sbPath.Length > 0)
                            sbPath.Append(" or");

                        sbPath.Append(string.Format(" relativePath like '%{0}%'", SQLiteClass3.ReplaceSqlStr(keyWord.LikePath)));
                    }

                    if (sbPath.Length > 0)
                        sbWhere.Append(string.Format(" and ({0})", sbPath.ToString()));

                    string sql = "SELECT * FROM Files " + sbWhere.ToString();// where relativePath like '%CallHistory%' or relativePath like '%Notes%' 

                    DataTable dt = SQLiteClass3.ExecuteSQL(sql, conn);
                    if (dt == null)
                    {
                        result.Type = MBErrorType.ManifestDbSqlError;
                        result.ErrMsgAppendLine(string.Format("dt is null. \r\n sql = {0}", sql));
                        goto DoExit;
                    }

                    if (dt.Rows.Count == 0)
                    {
                        result.ErrMsgAppendLine(string.Format("dt.Rows.Count = 0. \r\n sql = {0}", sql));
                        goto DoExit;
                    }

                    bool isString = false;
                    if (dt.Columns["file"].DataType.Name.ToLower().Contains("string"))
                    {
                        isString = true;
                    }

                    foreach (DataRow dr in dt.Rows)
                    {
                        try
                        {
                            string fileID = Common.GetValue<string>(dr["fileID"], "");
                            string domain = Common.GetValue<string>(dr["domain"], "");
                            string relativePath = Common.GetValue<string>(dr["relativePath"], "");
                            int flags = Common.GetValue<int>(dr["flags"], 0);
                            byte[] arrBytes = null;

                            if (isString)
                            {
                                string strByte = Common.GetValue<string>(dr["file"], "");
                                if (!string.IsNullOrEmpty(strByte))
                                    arrBytes = Encoding.UTF8.GetBytes(strByte);
                            }
                            else
                            {
                                arrBytes = Common.GetValue<byte[]>(dr["file"], null);
                            }

                            MBFileRecord item = new MBFileRecord();
                            item.fileInfoPlist = arrBytes;
                            item.fileID = fileID;
                            item.domain = domain;
                            item.relativePath = relativePath;

                            //默认值
                            item.UserID = 501;
                            item.GroupID = 501;
                            item.InodeNumber = 0;
                            item.alwaysZero = 0;
                            item.alwaysNull = null;
                            item.data = null;
                            //item.Properties = new System.Collections.Specialized.NameValueCollection();
                            item.PropertyCount = 0;
                            item.ModifyTime = DateTime.Now.AddDays(-1);
                            item.AccessTime = DateTime.Now.AddDays(-1);
                            item.CreateTime = DateTime.Now.AddDays(-1);
                            item.FlagsInPlist = (byte)flags;
                            item.Mode = 0;
                            item.BackupFolder = dirBackup;

                            item.LoadInfoFromPlist();

                            string sku = string.Empty;

                            item.domainType = MBFileRecord.GetDomainType(item.domain, ref sku);

                            if (!string.IsNullOrEmpty(item.relativePath))
                            {
                                item.PathOnPhone = mbdb.FormatPath(item.domainType, item.relativePath, sku);

                                if (dev.VersionNumber >= 1000)
                                    item.PathOnPC = Path.Combine(dirBackup, item.fileID.Substring(0, 2), item.fileID);
                                else
                                    item.PathOnPC = Path.Combine(dirBackup, item.fileID);

                                if (dev.IsEncrypted && File.Exists(item.PathOnPC))
                                {
                                    FileInfo info = new FileInfo(item.PathOnPC);
                                    string itemName = string.Format("{0}_{1}_{2}_{3}", info.LastWriteTime.ToString("yyyyMMdd_HHmmss"), info.Length.ToString().PadLeft(12,'0'), item.fileID, Common.ReplaceWinIllegalName(Path.GetFileName(item.relativePath)));

                                    item.PathOnPC_Decrypt = Path.Combine(dirUDID, itemName);

                                    if (!DecryptFileWithDBBlob(mCurrentUID, Marshal.UnsafeAddrOfPinnedArrayElement<byte>(arrBytes, 0), arrBytes.Length, item.PathOnPC, item.PathOnPC_Decrypt))
                                    {
                                        string errMsg = string.Format("DecryptFileWithDBBlob failed. \r\n PathOnPC = {0}, PathOnPC_Decrypt = {1}", item.PathOnPC, item.PathOnPC_Decrypt);                                      
                                        LogMbdb(errMsg);

                                        result.ErrMsgAppendLine(errMsg);
                                        continue;
                                    }
                                }

                                result.ListData.Add(item);
                            }
                            //Console.WriteLine(string.Format("ReloadDB -> {0}/{1}", list.Count, dt.Rows.Count));
                        }
                        catch (Exception ex)
                        {
                            result.ErrMsgAppendLine(ex.Message);
                            Common.LogException(ex.ToString(), "FilterDataFromBackup.foreach");
                        }
                    }

                    dt = null;

                }
                catch (Exception ex)
                {
                    result.ErrMsgAppendLine(ex.Message);
                    Common.LogException(ex.ToString(), "FilterDataFromBackup");
                }              
            }

        DoExit:
            try
            {
                if (conn != null && conn.State != ConnectionState.Closed)
                    conn.Close();
            }
            catch (Exception ex)
            {
                result.ErrMsgAppendLine(ex.Message);
                Common.LogException(ex.ToString(), "FilterDataFromBackup.Close");
            }

            LogMbdb(MyJson.SerializeToJsonString(result));

            return result;
        }

        #endregion

        public static bool CheckIsNeedDiskFullAccess(string strDir)
        {
            return CheckIsNeedDiskFullAccess(strDir, false);
        }

        public static bool CheckIsNeedDiskFullAccess(string strDir, bool checkManifestPlist)
        {
            bool blnResult = false;

#if MAC
            try
            {
                if (!Directory.Exists(strDir))
                    goto DoExit;

                string[] arrDir = Directory.GetDirectories(strDir);
                //string[] arrFile = Directory.GetFiles(strDir);

                if (!checkManifestPlist)
                    goto DoExit;

                string strManifest = Path.Combine(strDir, "Manifest.plist");

                if (!File.Exists(strManifest))
                    goto DoExit;

                byte[] arrData = File.ReadAllBytes(strManifest);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "mbdb.CheckIsNeedDiskFullAccess");

                if (ex is UnauthorizedAccessException)
                    blnResult = true;
            }
#endif

        DoExit:
            return blnResult;
        }

        public static iPhoneDevice GetDeviceFromManifestPlist(string dirBackup)
        {
            iPhoneDevice dev = new iPhoneDevice("", "");

            try
            {
                string strManifest = Path.Combine(dirBackup, "Manifest.plist");
                if (!File.Exists(strManifest))
                    goto DoExit;

                byte[] arrData = File.ReadAllBytes(strManifest);
                Dictionary<object, object> dict = CoreFoundation.ManagedPropertyListFromXMLData(arrData) as Dictionary<object, object>;
                if (dict == null)
                    goto DoExit;

                if (dict.ContainsKey("IsEncrypted") && (bool)dict["IsEncrypted"])
                    dev.IsEncrypted = (bool)dict["IsEncrypted"];

                if (dict.ContainsKey("Lockdown"))
                {
                    Dictionary<object, object> dictLockdown = dict["Lockdown"] as Dictionary<object, object>;
                    if (dictLockdown.ContainsKey("DeviceName"))
                        dev.DeviceName = dictLockdown["DeviceName"].ToString();

                    if (dictLockdown.ContainsKey("ProductType"))
                        dev.ProductType = dictLockdown["ProductType"].ToString();

                    if (dictLockdown.ContainsKey("SerialNumber"))
                        dev.SerialNumber = dictLockdown["SerialNumber"].ToString();

                    if (dictLockdown.ContainsKey("UniqueDeviceID"))
                        dev.UniqueDeviceID = dictLockdown["UniqueDeviceID"].ToString();

                    if (dictLockdown.ContainsKey("ProductVersion"))
                    {
                        dev.ProductVersion = dictLockdown["ProductVersion"].ToString();
                        dev.VersionNumber = Common.GetVersionNumber(dev.ProductVersion);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup.GetDeviceFromManifestPlist");
            }

        DoExit:
            return dev;
        }

        public static bool CheckIsEncrypted(string dirBackup)
        {           
            iPhoneDevice dev = GetDeviceFromManifestPlist(dirBackup);

            return dev.IsEncrypted;
        }

        private static byte[] ReverseBytes(byte[] arrData, int offset, int count)
        {
            byte[] arrBuffer = new byte[count - 1 + 1];

            int num = count;
            for (int i = offset; i <= (offset + count) - 1; i++)
            {
                num--;
                arrBuffer[num] = arrData[i];
            }

            return arrBuffer;
        }

        private static short ToInt16(byte[] value, int startIndex)
        {
            if (BitConverter.IsLittleEndian)
                return BitConverter.ToInt16(ReverseBytes(value, startIndex, 2), 0);

            return BitConverter.ToInt16(value, startIndex);
        }

        private static int ToInt32(byte[] value, int startIndex)
        {
            if (BitConverter.IsLittleEndian)
                return BitConverter.ToInt32(ReverseBytes(value, startIndex, 4), 0);

            return BitConverter.ToInt32(value, startIndex);
        }

        private static long ToInt64(byte[] value, int startIndex)
        {
            if (BitConverter.IsLittleEndian)
                return BitConverter.ToInt64(ReverseBytes(value, startIndex, 8), 0);

            return BitConverter.ToInt64(value, startIndex);
        }

        private static UInt16 ToUInt16(byte[] value, int startIndex)
        {
            if (BitConverter.IsLittleEndian)
                return BitConverter.ToUInt16(ReverseBytes(value, startIndex, 2), 0);

            return BitConverter.ToUInt16(value, startIndex);
        }

        private static UInt32 ToUInt32(byte[] value, int startIndex)
        {
            if (BitConverter.IsLittleEndian)
                return BitConverter.ToUInt32(ReverseBytes(value, startIndex, 4), 0);

            return BitConverter.ToUInt32(value, startIndex);
        }

        private static int FromHex(char c)
        {
            if (c >= '0' && c <= '9')
                return Asc(c) - Asc('0');

            if (c >= 'A' && c <= 'F')
                return Asc(c) - Asc('A') + 10;

            if (c >= 'a' && c <= 'f')
                return Asc(c) - Asc('a') + 10;

            return 0;
        }

        public static int Asc(char cStr)
        {
            int num2 = Convert.ToInt32(cStr);
            if (num2 < 0x80)
                return num2;

            int num = 0;
            byte[] arrBuffer = null;

            try
            {
                Encoding fileIOEncoding = Encoding.Default;
                char[] chars = new char[] { cStr };
                if (fileIOEncoding.IsSingleByte)
                {
                    arrBuffer = new byte[1];
                    int num3 = fileIOEncoding.GetBytes(chars, 0, 1, arrBuffer, 0);
                    return arrBuffer[0];
                }

                arrBuffer = new byte[2];
                if (fileIOEncoding.GetBytes(chars, 0, 1, arrBuffer, 0) == 1)
                    return arrBuffer[0];

                if (BitConverter.IsLittleEndian)
                {
                    byte num4 = arrBuffer[0];
                    arrBuffer[0] = arrBuffer[1];
                    arrBuffer[1] = num4;
                }
                num = BitConverter.ToInt16(arrBuffer, 0);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup.Asc");

                throw ex;
            }
            return num;
        }

        public static char ChrW(int CharCode)
        {
            if ((CharCode < -32768) || (CharCode > 0xffff))
                throw new ArgumentException("Argument_RangeTwoBytes1 CharCode" + CharCode.ToString());

            return Convert.ToChar((int)(CharCode & 0xffff));
        }

        private static char ToHex(int value)
        {
            value = value & 0xF;

            if (value >= 0 && value <= 9)
                return ChrW(System.Convert.ToInt32(Asc('0') + value));
            else
                return ChrW(System.Convert.ToInt32(Asc('A') + (value - 10)));
        }

        private static string ToHex(byte[] arrData, params int[] arrSpaces)
        {
            StringBuilder sb = new StringBuilder(arrData.Length * 3);

            int intN = 0;
            int intP = 0;
            for (int intI = 0; intI <= arrData.Length - 1; intI++)
            {
                if (intN < arrSpaces.Length && intI == intP + arrSpaces[intN])
                {
                    sb.Append(' ');
                    intP += arrSpaces[intN];
                    intN++;
                }
                sb.Append(ToHex(System.Convert.ToInt32(arrData[intI] >> 4)));
                sb.Append(ToHex(System.Convert.ToInt32(arrData[intI] & 15)));
            }

            return sb.ToString();
        }

        private static char ToHexLow(int value)
        {
            value = value & 0xF;

            if (value >= 0 && value <= 9)
                return ChrW(System.Convert.ToInt32(Asc('0') + value));
            else
                return ChrW(System.Convert.ToInt32(Asc('a') + (value - 10)));
        }

        private static string ReadStringHex(FileStream fs)
        {
            string strText = null;

            byte bytHigh = (byte)(fs.ReadByte());
            byte bytLow = (byte)(fs.ReadByte());

            if (bytHigh == 255 && bytLow == 255)
                goto DO_EXIT;

            int intLen = bytHigh * 256 + bytLow;
            byte[] arrText = new byte[intLen];
            fs.Read(arrText, 0, arrText.Length);

            int index = 0;
            for (index = 0; index <= arrText.Length - 1; index++)
            {
                if (arrText[index] < 32 || arrText[index] >= 128)
                    break;
            }

            if (index == arrText.Length)
                strText = System.Text.Encoding.UTF8.GetString(arrText);
            else
                strText = Common.ToHexString(arrText) + "\r";

            if (strText != null)
                strText = strText.Normalize(NormalizationForm.FormC);

        DO_EXIT:
            return strText;
        }

        private static string ReadString(FileStream fs)
        {
            string strText = null;

            byte bytHigh = (byte)(fs.ReadByte());
            byte bytLow = (byte)(fs.ReadByte());

            if (bytHigh == 255 && bytLow == 255)
                goto DO_EXIT;

            int intLen = bytHigh * 256 + bytLow;
            byte[] arrText = new byte[intLen];
            fs.Read(arrText, 0, arrText.Length);

            strText = System.Text.Encoding.UTF8.GetString(arrText);
            strText = strText.Normalize(NormalizationForm.FormC);

        DO_EXIT:
            return strText;
        }

        private static byte[] ReadRaw(FileStream fs, int len)
        {
            byte[] arrData = new byte[len];
            fs.Read(arrData, 0, arrData.Length);

            return arrData;
        }

        public static List<MBFileRecord> Reload(string dirBackup)
        {
            return Reload(dirBackup, false);
        }

        public static List<MBFileRecord> Reload(string dirBackup, bool isLoadTotal)
        {
            List<MBFileRecord> list = new List<MBFileRecord>();

            //string strFile = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) + "\\a.txt";
            //StreamWriter sw = new StreamWriter(strFile, false, Encoding.UTF8);

            string strMBDB = Path.Combine(dirBackup, "Manifest.mbdb");
            string strDB = Path.Combine(dirBackup, "Manifest.db");
            string strVer = GetBackupVersion(dirBackup);

            if (File.Exists(strDB))
            {
                Common.Log(string.Format("Reload Manifest.db {0}", strDB), true);
                list = ReloadDB(strDB, dirBackup, true);
            }

            if (File.Exists(strMBDB))// && Common.CompareVer(strVer, "10.0", 4) < 0
            {
                Common.Log(string.Format("Reload Manifest.mbdb {0}", strMBDB), true);
                list = ReloadMBDB(strMBDB, dirBackup, isLoadTotal);
            }

            return list;
        }

        private static string GetBackupVersion(string dirBackup)
        {
            string strVer = "";
            try
            {
                string strInfo = Path.Combine(dirBackup, "Info.plist");
                if (File.Exists(strInfo))
                {
                    Dictionary<object, object> dict = CoreFoundation.ManagedPropertyListFromXMLData(strInfo) as Dictionary<object, object>;
                    if (dict.ContainsKey("Product Version"))
                    {
                        strVer = (string)dict["Product Version"];
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup_GetBackupVersion");
            }
            return strVer;
        }

        public static List<MBFileRecord> ReloadMBDB(string strMBDB, string dirBackup, bool isLoadTotal)
        {
            List<MBFileRecord> list = new List<MBFileRecord>();

            FileStream fs = null;
            try
            {
                if (!File.Exists(strMBDB))
                    goto DO_EXIT;

                iPhoneDevice dev = GetDeviceFromManifestPlist(dirBackup);

                byte[] arrData = null;

                fs = new FileStream(strMBDB, FileMode.Open, FileAccess.Read);
                arrData = ReadRaw(fs, 6);
                if (System.BitConverter.ToString(arrData, 0) != "6D-62-64-62-05-00")
                {
                    Common.LogException(string.Format("bad .mbdb file  {0}", strMBDB), "ReloadMBDB");
                    goto DO_EXIT;
                }

                SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider();
                while (fs.Position < fs.Length - 1)
                {
                    try
                    {
                        MBFileRecord item = new MBFileRecord();
                        item.domain = ReadString(fs);
                        item.relativePath = ReadString(fs);
                        item.LinkTarget = ReadString(fs);
                        item.DataHash = ReadStringHex(fs);
                        item.alwaysNull = ReadStringHex(fs);

                        arrData = ReadRaw(fs, 40);
                        item.data = ToHex(arrData, new int[] { 2, 4, 4, 4, 4, 4, 4, 4, 8, 1, 1 });
                        item.Mode = ToUInt16(arrData, 0);
                        item.alwaysZero = ToInt32(arrData, 2);
                        item.InodeNumber = ToUInt32(arrData, 6);
                        item.UserID = (uint)(ToInt32(arrData, 10));
                        item.GroupID = ToInt32(arrData, 14);
                        item.ModifyTime = Common.ConvertToPcTime(ToUInt32(arrData, 18), DateTime.Parse("1970-01-01"));
                        item.AccessTime = Common.ConvertToPcTime(ToUInt32(arrData, 22), DateTime.Parse("1970-01-01"));
                        item.CreateTime = Common.ConvertToPcTime(ToUInt32(arrData, 26), DateTime.Parse("1970-01-01"));
                        item.Size = ToInt64(arrData, 30);
                        item.flags = arrData[38];
                        item.PropertyCount = arrData[39];

                        item.Properties = new System.Collections.Specialized.NameValueCollection();
                        for (int index = 0; index <= item.PropertyCount - 1; index++)
                        {
                            string strName = ReadString(fs);
                            string strValue = ReadStringHex(fs);

                            item.Properties.Add(strName, strValue);
                        }

                        string strKey = string.Empty;
                        if (string.IsNullOrEmpty(item.relativePath))
                            strKey = item.domain;
                        else
                            strKey = string.Format("{0}-{1}", item.domain, item.relativePath);

                        //if (item.Domain.StartsWith("AppDomain-com.tencent.xin", StringComparison.OrdinalIgnoreCase))
                        //{
                        //Console.WriteLine(strKey);
                        //}
                        //sw.WriteLine(strKey);

                        arrData = sha1.ComputeHash(Encoding.UTF8.GetBytes(strKey));
                        item.fileID = Common.ToHexString(arrData);

                        //if (strKey == "HomeDomain-Library/Notes/notes.sqlite")
                        //{
                        //    string strTmp = Common.GetSha1Hex("CameraRollDomain-Media/DCIM/100APPLE/IMG_0007.JPG");
                        //}
                        //

                        string dirSave = dirBackup;
                        string sku = string.Empty;
                        item.domainType = MBFileRecord.GetDomainType(item.domain, ref sku);

                        if (!string.IsNullOrEmpty(item.domain) && !string.IsNullOrEmpty(item.relativePath))
                        {
                            item.PathOnPhone = mbdb.FormatPath(item.domainType, item.relativePath, sku);

                            if (dev.VersionNumber >= 1000)
                                dirSave = Path.Combine(dirBackup, item.fileID.Substring(0, 2));

                            if (dev.IsEncrypted)
                            {
                                byte[] arrDataNew = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(item.PathOnPhone));
                                item.PathOnPC = Path.Combine(dirBackup, System.Convert.ToString(iPhoneDevice.Unback_Dir + Path.DirectorySeparatorChar.ToString() + Common.ToHexString(arrDataNew)));
                            }
                            else
                            {
                                item.PathOnPC = Path.Combine(dirSave, item.fileID);
                            }

                            list.Add(item);
                        }
                        else if (isLoadTotal)
                        {
                            list.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "Reload_while_ReloadMBDB");
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Reload_ReloadMBDB");
            }

        DO_EXIT:
            if (fs != null)
                fs.Close();

            return list;
        }

        public static List<MBFileRecord> ReloadDB(string strDB, string dirBackup, bool loadDetail, bool isLoadTotal = true)
        {
            List<MBFileRecord> list = new List<MBFileRecord>();

            SQLiteConnection conn = null;
            try
            {
                iPhoneDevice dev = GetDeviceFromManifestPlist(dirBackup);

                conn = SQLiteClass3.CreateConnectionFromFile(strDB);

                if (conn == null)
                    Common.Log(string.Format("ReloadDB.CreateConnectionFromFile->{0}", strDB), "ReloadDB", true);

                //string sql = @"SELECT * FROM Files ORDER BY domain,relativePath;";
                string sql = @"SELECT * FROM Files;";// where relativePath like '%CallHistory%' or relativePath like '%Notes%' 
                DataTable dt = SQLiteClass3.ExecuteSQL(sql, conn);
                if (dt != null && dt.Rows.Count > 0)
                {
                    SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider();
                    bool isString = false;
                    if (dt.Columns["file"].DataType.Name.ToLower().Contains("string"))
                    {
                        isString = true;
                    }
                    
                    foreach (DataRow dr in dt.Rows)
                    {
                        try
                        {
                            string fileID = Common.GetValue<string>(dr["fileID"], "");
                            string domain = Common.GetValue<string>(dr["domain"], "");
                            string relativePath = Common.GetValue<string>(dr["relativePath"], "");
                            int flags = Common.GetValue<int>(dr["flags"], 0);
                            byte[] arrBytes = null;

                            if (isString)
                            {
                                string strByte = Common.GetValue<string>(dr["file"], "");
                                if (!string.IsNullOrEmpty(strByte))
                                {
                                    arrBytes = Encoding.UTF8.GetBytes(strByte);
                                }
                            }
                            else
                            {
                                arrBytes = Common.GetValue<byte[]>(dr["file"], null);
                            }

                            MBFileRecord item = new MBFileRecord();
                            item.fileInfoPlist = arrBytes;
                            item.fileID = fileID;
                            item.domain = domain;                            
                            item.relativePath = relativePath;

                            //默认值
                            item.UserID = 501;
                            item.GroupID = 501;
                            item.InodeNumber = 0;
                            item.alwaysZero = 0;
                            item.alwaysNull = null;
                            item.data = null;
                            //item.Properties = new System.Collections.Specialized.NameValueCollection();
                            item.PropertyCount = 0;
                            item.ModifyTime = DateTime.Now.AddDays(-1);
                            item.AccessTime = DateTime.Now.AddDays(-1);
                            item.CreateTime = DateTime.Now.AddDays(-1);
                            item.FlagsInPlist = (byte)flags;
                            item.Mode = 0;
                            item.BackupFolder = dirBackup;

                            if (loadDetail)
                                item.LoadInfoFromPlist();

                            string dirSave = dirBackup;
                            string sku = string.Empty;
                            item.domainType = MBFileRecord.GetDomainType(item.domain, ref sku);

                            if (!string.IsNullOrEmpty(item.domain) && !string.IsNullOrEmpty(item.relativePath))
                            {
                                item.PathOnPhone = mbdb.FormatPath(item.domainType, item.relativePath, sku);

                                if (dev.VersionNumber >= 1000)
                                    dirSave = Path.Combine(dirBackup, item.fileID.Substring(0, 2));

                                if (dev.IsEncrypted)
                                {
                                    byte[] arrData = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(item.PathOnPhone));
                                    item.PathOnPC = Path.Combine(dirBackup, System.Convert.ToString(iPhoneDevice.Unback_Dir + Path.DirectorySeparatorChar.ToString() + Common.ToHexString(arrData)));
                                }
                                else
                                {
                                    item.PathOnPC = Path.Combine(dirSave, item.fileID);
                                }

                                list.Add(item);
                            }
                            else if (isLoadTotal)
                            {
                                list.Add(item);
                            }

                            //Console.WriteLine(string.Format("ReloadDB -> {0}/{1}", list.Count, dt.Rows.Count));
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "iTunesBackup_ReloadDB_foreach");
                        }
                    }
                }
                dt = null;

                //list.Sort(new MBFileRecordCompare());
                //if (list.Count > 0)
                //{
                //    foreach (MBFileRecord item in list)
                //        Console.WriteLine(item.InodeNumber);
                //}
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup_ReloadDB");
            }
            try
            {
                if (conn != null && conn.State != ConnectionState.Closed)
                    conn.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup_ReloadDB_1");
            }
            return list;
        }

        public static bool WriteDBWeixin(string dirBackup, string strSku)
        {
            //ios10是数据库操作，把微信Document以后的数据都删除掉
            string strDB = Path.Combine(dirBackup, "Manifest.db");
            bool blnUpdateSucceed = true;
            SQLiteConnection conn = null;
            try
            {
                conn = SQLiteClass3.CreateConnectionFromFile(strDB);
                string sql = string.Format(@"
DELETE 
FROM 
  Files  
WHERE 
  domain<>'AppDomain-{0}' 
OR relativePath like 'Library%';", strSku);

                int intResult = SQLiteClass3.ExecuteNoneQuery(sql, conn);
                if (intResult < 0)
                {
                    blnUpdateSucceed = false;
                }
            }
            catch (Exception ex)
            {
                blnUpdateSucceed = false;
                Common.LogException(ex.ToString(), "iTunesBackup_WriteDBWeixin");
            }

            if (conn != null && conn.State != ConnectionState.Closed)
                conn.Close();

            return blnUpdateSucceed;
        }

        public static bool WriteDBWeixin(string dirBackup, List<string> listDelItem)
        {
            if (listDelItem == null || listDelItem.Count <= 0)
            {
                return false;
            }

            string strDB = Path.Combine(dirBackup, "Manifest.db");
            bool blnUpdateSucceed = true;
            SQLiteConnection conn = null;
            try
            {
                conn = SQLiteClass3.CreateConnectionFromFile(strDB);
                StringBuilder bsSQL = new StringBuilder();
                string sql = @"
DELETE 
FROM 
  Files  
WHERE 
  fileID='{0}';";
                foreach (string item in listDelItem)
                {
                    bsSQL.AppendLine(string.Format(sql, item));
                }
                int intResult = SQLiteClass3.ExecuteNoneQuery(bsSQL.ToString(), conn);
                if (intResult < 0)
                {
                    blnUpdateSucceed = false;
                }
            }
            catch (Exception ex)
            {
                blnUpdateSucceed = false;
                Common.LogException(ex.ToString(), "iTunesBackup_WriteDBWeixin");
            }

            if (conn != null && conn.State != ConnectionState.Closed)
                conn.Close();

            return blnUpdateSucceed;
        }

        public static bool WriteDBSMS(string dirBackup)
        {
            //ios10是数据库操作，把短信以外的数据都删除掉
            string strDB = Path.Combine(dirBackup, "Manifest.db");
            bool blnUpdateSucceed = true;
            SQLiteConnection conn = null;
            try
            {
                conn = SQLiteClass3.CreateConnectionFromFile(strDB);
                string sql = @"
DELETE 
FROM 
  Files  
WHERE 
  (domain<>'HomeDomain' AND domain<>'MediaDomain') 
OR fileid NOT IN(select fileid from files where relativePath='' OR 
                                                relativePath='Library' OR 
                                                relativePath LIKE 'Library/SMS%');";

                int intResult = SQLiteClass3.ExecuteNoneQuery(sql, conn);
                if (intResult < 0)
                {
                    blnUpdateSucceed = false;
                }
            }
            catch (Exception ex)
            {
                blnUpdateSucceed = false;
                Common.LogException(ex.ToString(), "iTunesBackup_WriteDBSMS");
            }

            if (conn != null && conn.State != ConnectionState.Closed)
                conn.Close();

            return blnUpdateSucceed;
        }

        public static string FormatPath(MBFileDomainType domainType, string strRelativePath, string sku = "")
        {
            string pathOnPhone = strRelativePath;

            switch (domainType)
            {
                case MBFileDomainType.WirelessDomain:
                    pathOnPhone = string.Format("/var/wireless/{0}", strRelativePath);
                    break;
                case MBFileDomainType.ManagedPreferencesDomain:
                    pathOnPhone = string.Format("/var/Managed Preferences/{0}", strRelativePath);
                    break;
                case MBFileDomainType.MediaDomain:
                    pathOnPhone = string.Format("/var/mobile/{0}", strRelativePath);
                    break;
                case MBFileDomainType.SystemPreferencesDomain:
                    pathOnPhone = string.Format("/var/preferences/{0}", strRelativePath);
                    break;
                case MBFileDomainType.CameraRollDomain:
                    pathOnPhone = string.Format("/var/mobile/{0}", strRelativePath);
                    break;
                case MBFileDomainType.RootDomain:
                    pathOnPhone = string.Format("/var/root/{0}", strRelativePath);
                    break;
                case MBFileDomainType.MobileDeviceDomain:
                    pathOnPhone = string.Format("/var/MobileDevice/{0}", strRelativePath);
                    break;
                case MBFileDomainType.KeychainDomain:
                    pathOnPhone = string.Format("/var/Keychains/{0}", strRelativePath);
                    break;
                case MBFileDomainType.HomeDomain:
                    pathOnPhone = string.Format("/var/mobile/{0}", strRelativePath);
                    break;
                case MBFileDomainType.DatabaseDomain:
                    pathOnPhone = string.Format("/var/db/{0}", strRelativePath);
                    break;

                case MBFileDomainType.AppDomain:
                case MBFileDomainType.AppDomainGroup:
                case MBFileDomainType.AppDomainPlugin:
                    pathOnPhone = string.Format("/var/mobile/Applications/{0}/{1}", sku, strRelativePath);
                    break;

                default:
                    pathOnPhone = strRelativePath;
                    break;
            }

            return pathOnPhone;
        }

        public static void Write(string dirBackup)
        {
            Write(dirBackup, new List<string>());
        }

        public static void Write(string dirBackup, List<string> listDelItem)
        {
            FileStream fs = null;

            try
            {
                List<MBFileRecord> lstData = Reload(dirBackup, true);

                string strMBDB = Path.Combine(dirBackup, "Manifest.mbdb");
                if (File.Exists(strMBDB))
                    File.Move(strMBDB, strMBDB + "." + DateTime.Now.ToString("yyyyMMddHHmmss"));

                bool blnEncrypt = CheckIsEncrypted(dirBackup);
                byte[] arrData = new byte[] { 109, 98, 100, 98, 5, 0 };

                fs = new FileStream(strMBDB, FileMode.Create, FileAccess.Write);
                WriteRaw(fs, arrData);
                BinaryWriter bs = new BinaryWriter(fs);

                int index3 = 0;
                foreach (MBFileRecord Item in lstData)
                {
                    index3++;
                    //if (index3 == 0x12bb)
                    //{ 
                    //}

                    //if (fs.Position >= 0x880ef)
                    //{

                    //}
                    bool blnContinue = false;
                    if (!string.IsNullOrEmpty(Item.PathOnPhone))
                    {
                        foreach (string strDel in listDelItem)
                        {
                            if (Item.PathOnPhone.StartsWith(strDel) && Item.PathOnPhone.TrimEnd('/').Length >= strDel.TrimEnd('/').Length)
                            {
                                Console.WriteLine(Item.PathOnPhone);
                                blnContinue = true;
                                break;
                            }
                        }
                    }

                    if (blnContinue)
                        continue;

                    WriteString(fs, Item.domain);
                    WriteString(fs, Item.relativePath);
                    WriteString(fs, Item.LinkTarget);
                    WriteHexString(fs, Item.DataHash);
                    WriteHexString(fs, Item.alwaysNull);

                    WriteUShort(fs, Item.Mode);
                    WriteInteger(fs, Item.alwaysZero);
                    WriteUInteger(fs, Item.InodeNumber);
                    WriteUInteger(fs, Item.UserID);
                    WriteInteger(fs, Item.GroupID);
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.ModifyTime, DateTime.Parse("1970-01-01")));
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.AccessTime, DateTime.Parse("1970-01-01")));
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.CreateTime, DateTime.Parse("1970-01-01")));

                    //if (File.Exists(Item.PathOnPC))
                    //{
                    //    FileInfo info = new FileInfo(Item.PathOnPC);

                    //    if (info.Length != Item.FileLength)
                    //        Console.WriteLine(Item.PathOnPC + "\t" + Item.PathOnPhone + "\tPC: " + Convert.ToString(info.Length, 16) + "\tPhone: " + Convert.ToString(Item.FileLength, 16) + "\tDiff: " + Convert.ToString(info.Length - Item.FileLength, 16));

                    //    WriteLong(fs, info.Length);
                    //}
                    //else
                    {
                        WriteLong(fs, Item.Size);
                    }



                    bs.Write(Item.flags);
                    bs.Write(Item.PropertyCount);

                    foreach (string itemProperties in Item.Properties.Keys)
                    {
                        WriteString(fs, itemProperties);
                        WriteHexString(fs, Item.Properties[itemProperties]);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup.Write");
            }

            if (fs != null)
                fs.Close();
        }

        public static void Write(string dirBackup, List<MBFileRecord> listItem)
        {
            //写入现有的文件到数据库中
            FileStream fs = null;

            try
            {
                List<MBFileRecord> lstData = listItem;

                string strMBDB = Path.Combine(dirBackup, "Manifest.mbdb");
                if (File.Exists(strMBDB))
                    File.Move(strMBDB, strMBDB + "." + DateTime.Now.ToString("yyyyMMddHHmmss"));

                bool blnEncrypt = CheckIsEncrypted(dirBackup);
                byte[] arrData = new byte[] { 109, 98, 100, 98, 5, 0 };

                fs = new FileStream(strMBDB, FileMode.Create, FileAccess.Write);
                WriteRaw(fs, arrData);
                BinaryWriter bs = new BinaryWriter(fs);

                foreach (MBFileRecord Item in lstData)
                {
                    WriteString(fs, Item.domain);
                    WriteString(fs, Item.relativePath);
                    WriteString(fs, Item.LinkTarget);
                    WriteHexString(fs, Item.DataHash);
                    WriteHexString(fs, Item.alwaysNull);

                    WriteUShort(fs, Item.Mode);
                    WriteInteger(fs, Item.alwaysZero);
                    WriteUInteger(fs, Item.InodeNumber);
                    WriteUInteger(fs, Item.UserID);
                    WriteInteger(fs, Item.GroupID);
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.ModifyTime, DateTime.Parse("1970-01-01")));
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.AccessTime, DateTime.Parse("1970-01-01")));
                    WriteInteger(fs, (int)Common.ConvertToPhoneTime(Item.CreateTime, DateTime.Parse("1970-01-01")));

                    WriteLong(fs, Item.Size);

                    bs.Write(Item.flags);
                    bs.Write(Item.PropertyCount);

                    foreach (string itemProperties in Item.Properties.Keys)
                    {
                        WriteString(fs, itemProperties);
                        WriteHexString(fs, Item.Properties[itemProperties]);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iTunesBackup.Write");
            }

            if (fs != null)
                fs.Close();
        }

        private static void WriteRaw(FileStream fs, byte[] arrData)
        {
            fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteString(FileStream fs, string strValue)
        {
            byte[] arrData = null;
            byte[] arrDataLen = null;

            if (strValue == null)
            {
                arrDataLen = new byte[] { 255, 255 };
            }
            else if (strValue.Length == 0)
            {
                arrDataLen = new byte[] { 0, 0 };
            }
            else
            {
                arrData = Encoding.UTF8.GetBytes(strValue);
                byte[] arrTemp = BitConverter.GetBytes((short)arrData.Length);
                arrDataLen = new byte[2];
                arrDataLen[0] = arrTemp[1];
                arrDataLen[1] = arrTemp[0];
            }

            fs.Write(arrDataLen, 0, arrDataLen.Length);
            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteHexString(FileStream fs, string strValue)
        {
            byte[] arrData = null;
            byte[] arrDataLen = null;

            if (strValue == null)
            {
                arrDataLen = new byte[] { 255, 255 };
            }
            else if (strValue.Length == 0)
            {
                arrDataLen = new byte[] { 0, 0 };
            }
            else
            {
                if (strValue.EndsWith("\r"))
                    arrData = Common.FromHexString(strValue.TrimEnd('\r'));
                else
                    arrData = Encoding.UTF8.GetBytes(strValue);

                byte[] arrTemp = System.BitConverter.GetBytes((short)arrData.Length);
                arrDataLen = new byte[2];
                arrDataLen[0] = arrTemp[1];
                arrDataLen[1] = arrTemp[0];
            }

            fs.Write(arrDataLen, 0, arrDataLen.Length);
            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteByte(FileStream fs, byte byt)
        {
            fs.WriteByte(byt);
        }

        private static void WriteUShort(FileStream fs, ushort shortValue)
        {
            byte[] arrTemp = System.BitConverter.GetBytes(shortValue);
            List<byte> list = new List<byte>();

            for (int intI = arrTemp.Length - 1; intI >= 0; intI--)
                list.Add(arrTemp[intI]);

            byte[] arrData = list.ToArray();

            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteInteger(FileStream fs, int intValue)
        {
            byte[] arrTemp = System.BitConverter.GetBytes(intValue);
            List<byte> list = new List<byte>();

            for (int intI = arrTemp.Length - 1; intI >= 0; intI--)
                list.Add(arrTemp[intI]);

            byte[] arrData = list.ToArray();

            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteUInteger(FileStream fs, uint uValue)
        {
            byte[] arrTemp = System.BitConverter.GetBytes(uValue);
            List<byte> list = new List<byte>();

            for (int intI = arrTemp.Length - 1; intI >= 0; intI--)
                list.Add(arrTemp[intI]);

            byte[] arrData = list.ToArray();

            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        private static void WriteLong(FileStream fs, long lngValue)
        {
            byte[] arrTemp = System.BitConverter.GetBytes(lngValue);
            List<byte> list = new List<byte>();

            for (int intI = arrTemp.Length - 1; intI >= 0; intI--)
                list.Add(arrTemp[intI]);

            byte[] arrData = list.ToArray();

            if (arrData != null && arrData.Length > 0)
                fs.Write(arrData, 0, arrData.Length);
        }

        public static bool SaveMBDB(string dirBackup, List<MBFileRecord> list)
        {
            bool blnResult = false;

            FileStream fs = null;
            try
            {
                string strMBDB = Path.Combine(dirBackup, "Manifest.mbdb");

                Folder.CheckFolder(dirBackup);

                fs = new FileStream(strMBDB, FileMode.Open, FileAccess.Write);

                WriteRaw(fs, new byte[] { 0x6d, 0x62, 0x64, 0x62, 0x05, 0x00 });

                SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider();
                foreach (MBFileRecord item in list)
                {
                    WriteString(fs, item.domain);
                    WriteString(fs, item.relativePath);
                    WriteString(fs, item.LinkTarget);
                    WriteHexString(fs, item.DataHash);
                    WriteHexString(fs, item.alwaysNull);

                    WriteUShort(fs, item.Mode);
                    WriteInteger(fs, item.alwaysZero);
                    WriteUInteger(fs, item.InodeNumber);
                    WriteUInteger(fs, item.UserID);
                    WriteInteger(fs, item.GroupID);
                    WriteUInteger(fs, (uint)Common.ConvertToPhoneTime(item.ModifyTime, DateTime.Parse("1970-01-01")));
                    WriteUInteger(fs, (uint)Common.ConvertToPhoneTime(item.AccessTime, DateTime.Parse("1970-01-01")));
                    WriteUInteger(fs, (uint)Common.ConvertToPhoneTime(item.CreateTime, DateTime.Parse("1970-01-01")));
                    WriteLong(fs, item.Size);
                    WriteByte(fs, item.flags);
                    WriteByte(fs, item.PropertyCount);

                    foreach (string keyProperty in item.Properties.AllKeys)
                    {
                        WriteString(fs, keyProperty);
                        WriteHexString(fs, item.Properties[keyProperty]);
                    }                 
                }

                blnResult = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SaveMBDB");
            }

            if (fs != null)
                fs.Close();

            return blnResult;
        }

        private static bool CreateDB(SQLiteConnection conn)
        {
            string sql = @"
DROP TABLE IF EXISTS [Properties];
CREATE TABLE [Properties](
  [key] TEXT,
  [value] BLOB,
  PRIMARY KEY([key])
);

DROP TABLE IF EXISTS [Files];
CREATE TABLE [Files](
  [fileID] TEXT,
  [domain] TEXT,
  [relativePath] TEXT,
  [flags] INTEGER,
  [file] BLOB,
  PRIMARY KEY([fileID])
);
CREATE INDEX [FilesDomainIdx]
ON [Files](
    [domain] ASC
);
CREATE INDEX [FilesFlagsIdx]
ON [Files](
  [flags] ASC
);
CREATE INDEX [FilesRelativePathIdx]
ON [Files](
  [relativePath] ASC
);
            ";

            try
            {
                string errmsg = string.Empty;
                SQLiteClass3.ExecuteNoneQuery(sql, conn, null, null, ref errmsg);
                return string.IsNullOrEmpty(errmsg);
            }
            catch
            {
                return false;
            }
        }

        public static bool SaveDB(string dirBackup, List<MBFileRecord> list, byte[] arrDbData, BackupRestoreEventArgs args)
        {
            bool blnResult = false;

            string strDB = string.Empty;
           SQLiteConnection conn = null;
            try
            {
                if (args == null)
                    args = new BackupRestoreEventArgs();

                bool isNewDB = (arrDbData == null || arrDbData.Length == 0);

                strDB = Path.Combine(dirBackup, "Manifest.db");

                Folder.CheckFolder(dirBackup);

                if (isNewDB)
                    Utility.DeleteFile(strDB);

                //File.Copy(@"d:\Users\Administrator\Desktop\Air\Backup\install_doRestore_00008030-000A31061188802E\Manifest_Famisafe_Install.db", strDB);

                File.WriteAllBytes(strDB, arrDbData);

                conn = SQLiteClass3.CreateConnectionFromFile(strDB);
                if (conn == null)
                {
                    args.ErrorMsg = "CreateConnectionFromFile failed -> " + strDB;
                    Common.Log(args.ErrorMsg, true);
                    goto DoExit;
                }

                if (isNewDB)
                    CreateDB(conn);

                int maxCount = 30;

                SQLiteParameter fileIDParameter = new SQLiteParameter("@fileID", DbType.String);
                SQLiteParameter domainParameter = new SQLiteParameter("@domain", DbType.String);
                SQLiteParameter pathParameter = new SQLiteParameter("@path", DbType.String);
                SQLiteParameter flagParameter = new SQLiteParameter("@flag", DbType.Int32);
                SQLiteParameter fileParameter = new SQLiteParameter("@file", DbType.Object);

                SQLiteCommand cmd = new SQLiteCommand();
                cmd.Connection = conn;
                cmd.CommandText = "insert or replace into Files(fileID, domain, relativePath, flags, file) values(@fileID, @domain, @path, @flag, @file)";
                cmd.CommandTimeout = maxCount;

                cmd.Parameters.Add(fileIDParameter);
                cmd.Parameters.Add(domainParameter);
                cmd.Parameters.Add(pathParameter);
                cmd.Parameters.Add(flagParameter);
                cmd.Parameters.Add(fileParameter);
                cmd.Prepare();

                IDbTransaction tran = conn.BeginTransaction();

                int itemCount = 0;
                foreach (MBFileRecord record in list)
                {
                    try
                    {
                        itemCount++;

                        fileIDParameter.Value = record.fileID;
                        domainParameter.Value = record.domain;
                        pathParameter.Value = record.relativePath;
                        flagParameter.Value = (int)record.flags;
                        fileParameter.Value = record.fileInfoPlist;

                        int count = cmd.ExecuteNonQuery();
                        if (itemCount >= maxCount)
                        {
                            tran.Commit();

                            tran = conn.BeginTransaction();
                            itemCount = 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(cmd.CommandText + "\r\n" + ex.ToString(), "SaveDB");
                    }
                }

                tran.Commit();
                tran.Dispose();

                cmd.Dispose();

                blnResult = true;
            }
            catch (Exception ex)
            {
                args.ErrorMsg = strDB + "\r\n" + ex.Message;
                Common.Log(args.ErrorMsg, true);
            }
            finally
            {
                if (conn != null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }

        DoExit:
            return blnResult;
        }

        public static bool MDM_CreatePlist_Manifest(string dirBackup, iPhoneDevice dev)
        {
            string s = "VkVSUwAAAAQAAAADVFlQRQAAAAQAAAABVVVJRAAAABA3inHV9cdF3IvQHNRXYGWwSE1DSwAAACgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAV1JBUAAAAAQAAAAAU0FMVAAAABQlcERi6tJpifVFj0kF9CHEpa61RklURVIAAAAEAAAnEFVVSUQAAAAQM304zYbSTmyv6+q1OzQKXkNMQVMAAAAEAAAAC1dSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAoVkEkJkpH1gU6UACDhsZyvwAFVAMHJrpE8oTzYJ8wz+gWwFA0Qi1TFFVVSUQAAAAQBySNuI3OS8uUmXC1OweJiUNMQVMAAAAEAAAACldSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAovT15SeSvXLhHZ0G/dIswGk68WjbL5qm1Tq5gxYcNY8YNWNYwoBtFT1VVSUQAAAAQCXNFRRtRTIu4AvWgs4DXVENMQVMAAAAEAAAACVdSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAogpfe0FNVV02SWH21P7t/K3EKtde/zcliy0AhOdaUuwM4xOE3HsbQG1VVSUQAAAAQjth+7XsbTtqOEOOaAmB2UUNMQVMAAAAEAAAACFdSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAoX+QmShPZUGn9EeakfiYNLSd0fTZKiTQ+0BNbd5Lm2qi4A+n89e8ncFVVSUQAAAAQzIJAaF87SY6mVfgXSL7dUkNMQVMAAAAEAAAAB1dSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAo7HkdWdEvgvgNfm7hVjncZJHJSFVyKPXfkz9nZKpB6IodmvkURrBw8lVVSUQAAAAQRJwq/oevQPm30SGJSMg8JUNMQVMAAAAEAAAABldSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAoRFKLUVKWM5oFimCR/fc0rK+oQCniuP/ubg0T/OuGQt47t83gHMWb4FVVSUQAAAAQuZ4e7mwYSaqESNr5RFZg10NMQVMAAAAEAAAABVdSQVAAAAAEAAAAA0tUWVAAAAAEAAAAAFdQS1kAAAAosG+sKCJMVQNPb65BG3cSEtLankhg6ZKNvluQ/xQ1an59OkN1EA5JDlVVSUQAAAAQQVc69M4SQOKhY0YW8pJ2LENMQVMAAAAEAAAABFdSQVAAAAAEAAAAAktUWVAAAAAEAAAAAFdQS1kAAAAodfjViaYeYP6ATsyNQZ2kAnq0VuFZoOIFiiz+Y9mUaAB+GPAbDNtM31VVSUQAAAAQUNVQg1WgRGO3+9wkNpkblkNMQVMAAAAEAAAAA1dSQVAAAAAEAAAAAktUWVAAAAAEAAAAAFdQS1kAAAAoB2WgXif6XPOmqf76Zv0lYrJ9CSIsrAUM00Wo2stYGVMEuUF8aY83ClVVSUQAAAAQp8mg/NjzTl+N4RTN2Js6TkNMQVMAAAAEAAAAAldSQVAAAAAEAAAAAktUWVAAAAAEAAAAAFdQS1kAAAAo9uqlzbojfNDoT1gU7rpbPLj7+xycZ+IJiFqwyWa9R6e54HvMOqznUFVVSUQAAAAQlT2QXhW+R3uwu0/fcjX22kNMQVMAAAAEAAAAAVdSQVAAAAAEAAAAAktUWVAAAAAEAAAAAFdQS1kAAAAo1ckh/fHntDXbmX9tN6O0r7MVNKth2HtWCTY+R9cFGI4Y4IGoUQeuSQ==";

            Dictionary<object, object> dictLockdown = new Dictionary<object, object>();
            dictLockdown.Add("BuildVersion", dev.BuildVersion);
            dictLockdown.Add("UniqueDeviceID", dev.UniqueDeviceID);
            dictLockdown.Add("SerialNumber", dev.SerialNumber);
            dictLockdown.Add("ProductType", dev.ProductType);
            dictLockdown.Add("DeviceName", dev.DeviceName);

            Dictionary<object, object> dictPlist = new Dictionary<object, object>();
            dictPlist.Add("BackupKeyBag", Convert.FromBase64String(s));
            dictPlist.Add("Date", DateTime.Now.AddMonths(-1).ToUniversalTime());
            dictPlist.Add("WasPasscodeSet", true);
            dictPlist.Add("IsEncrypted", false);
            dictPlist.Add("Lockdown", dictLockdown);

            Version devVersion = new Version(dev.ProductVersion);
            int major = devVersion.Major;
            if (major >= 10)
            {
                dictPlist.Add("SystemDomainsVersion", "24.0");
                dictPlist.Add("Version", "10.0");
            }
            else if (major >= 8)
            {
                dictPlist.Add("SystemDomainsVersion", "22.0");
                dictPlist.Add("Version", "9.1");
            }
            else if (major >= 7)
            {
                dictPlist.Add("SystemDomainsVersion", "20.0");
                dictPlist.Add("Version", "9.1");
            }
            else if (major >= 6)
            {
                dictPlist.Add("SystemDomainsVersion", "16.0");
                dictPlist.Add("Version", "9.1");
            }
            else if (major >= 5)
            {
                dictPlist.Add("SystemDomainsVersion", "12.0");
                dictPlist.Add("Version", "9.0");
            }
            else
            {
                dictPlist.Add("SystemDomainsVersion", "3.0");
                dictPlist.Add("Version", "8.0");
            }

            string pathManifest = Path.Combine(dirBackup, "Manifest.plist");

            Folder.CheckFolder(dirBackup);
            Utility.DeleteFile(pathManifest);

            CoreFoundation.CreatePlist(dictPlist, pathManifest);

            return true;
        }

        public static bool MDM_CreatePlist_Status(string dirBackup, bool isDB)
        {
            Dictionary<object, object> dictPlist = new Dictionary<object, object>();

            dictPlist.Add("BackupState", "new");
            dictPlist.Add("Date", DateTime.Now.AddMonths(-1).ToUniversalTime());
            dictPlist.Add("IsFullBackup", false);
            dictPlist.Add("SnapshotState", "finished");
            dictPlist.Add("UUID", Guid.NewGuid().ToString());
            dictPlist.Add("Version", isDB ? "3.3" : "2.4");

            string pathPlist = Path.Combine(dirBackup, "Status.plist");

            Folder.CheckFolder(dirBackup);
            Utility.DeleteFile(pathPlist);

            CoreFoundation.CreatePlist(dictPlist, pathPlist);

            return true;
        }

        public static bool MDM_CreatePlist_Info(string dirBackup, iPhoneDevice dev)
        {
            Dictionary<object, object> dictPlist = new Dictionary<object, object>();

            //dictPlist.Add("Build Version", dev.BuildVersion);
            //dictPlist.Add("Device Name", dev.DeviceName);
            //dictPlist.Add("Display Name", dev.DeviceName);
            //dictPlist.Add("GUID", System.Guid.NewGuid().ToString("N"));

            //if (!string.IsNullOrEmpty(dev.ICCID))
            //    dictPlist.Add("ICCID", dev.ICCID);

            //dictPlist.Add("IMEI", dev.GetDeviceValue(DeviceInfoKey.InternationalMobileEquipmentIdentity));
            //dictPlist.Add("Last Backup Date", DateTime.Now);

            //dictPlist.Add("Phone Number", dev.PhoneNumber);
            //dictPlist.Add("Product Type", dev.ProductType);
            //dictPlist.Add("Product Version", dev.ProductVersion);
            //dictPlist.Add("Serial Number", dev.SerialNumber);
            //dictPlist.Add("Target Identifier", dev.Identifier);
            //dictPlist.Add("Target Type", "Device");
            dictPlist.Add("Unique Identifier", dev.UniqueDeviceID);
            dictPlist.Add("iTunes Version", "*********");

            dictPlist.Add("Applications", new Dictionary<object, object>());

            string pathPlist = Path.Combine(dirBackup, "Info.plist");

            Folder.CheckFolder(dirBackup);
            Utility.DeleteFile(pathPlist);

            CoreFoundation.CreatePlist(dictPlist, pathPlist);

            dictPlist.Clear();

            return true;
        }

        public static byte[] MDM_CreatePlist_CloudConfigurationDetails(MDMConfig config)
        {
            byte[] arrData = null;

            bool isUnlockMDM = config == null;

            List<object> listSkipSetup = new List<object>();
            listSkipSetup.Add("Android");
            listSkipSetup.Add("Appearance");
            listSkipSetup.Add("AppleID");
            listSkipSetup.Add("AppStore");
            listSkipSetup.Add("Biometric");
            listSkipSetup.Add("Diagnostics");
            listSkipSetup.Add("DisplayTone");
            listSkipSetup.Add("FileVault");
            listSkipSetup.Add("HomeButtonSensitivity");
            listSkipSetup.Add("iCloudDiagnostics");
            listSkipSetup.Add("CloudStorage");
            listSkipSetup.Add("iMessageAndFaceTime");
            listSkipSetup.Add("Location");
            listSkipSetup.Add("OnBoarding");
            listSkipSetup.Add("Passcode");
            listSkipSetup.Add("Payment");
            listSkipSetup.Add("Privacy");
            listSkipSetup.Add("Registration");
            listSkipSetup.Add("Restore");
            listSkipSetup.Add("RestoreCompleted");
            listSkipSetup.Add("UpdateCompleted");
            listSkipSetup.Add("ScreenTime");
            listSkipSetup.Add("ScreenSaver");
            listSkipSetup.Add("SIMSetup");
            listSkipSetup.Add("Siri");
            listSkipSetup.Add("SoftwareUpdate");
            listSkipSetup.Add("TapToSetup");
            listSkipSetup.Add("TOS");
            listSkipSetup.Add("WatchMigration");
            listSkipSetup.Add("Zoom");
            listSkipSetup.Add("Welcome");

            Dictionary<object, object> dictPlist = new Dictionary<object, object>();
            if (isUnlockMDM)
            {
                dictPlist.Add("AllowPairing", true);
                dictPlist.Add("CloudConfigurationUIComplete", true);
                dictPlist.Add("IsSupervised", false);
                dictPlist.Add("PostSetupProfileWasInstalled", true);
                dictPlist.Add("SkipSetup", listSkipSetup);
            }
            else
            {
                dictPlist.Add("AllowPairing", true);
                dictPlist.Add("CloudConfigurationUIComplete", false);
                dictPlist.Add("IsSupervised", true);
                dictPlist.Add("PostSetupProfileWasInstalled", true);
                dictPlist.Add("AwaitDeviceConfigured", false);
                dictPlist.Add("ConfigurationSource", 2);
                dictPlist.Add("ConfigurationWasApplied", false);
                dictPlist.Add("IsMDMUnremovable", true);
                dictPlist.Add("IsMandatory", true);
                dictPlist.Add("IsMultiUser", false);
                dictPlist.Add("ConfigurationURL", config.ConfigurationURL);
                dictPlist.Add("OrganizationAddress", config.OrganizationAddress);
                dictPlist.Add("OrganizationEmail", config.OrganizationEmail);
                dictPlist.Add("OrganizationName", config.OrganizationName);
                dictPlist.Add("OrganizationPhone", config.OrganizationPhone);
                dictPlist.Add("SkipSetup", listSkipSetup);
            }

            arrData = CoreFoundation.CreatePlistBinaryData(dictPlist);

            return arrData;
        }

        public static byte[] MDM_CreatePlist_FileInfo(MBFileRecord record)
        {
            byte[] arrData = null;

            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"">
<plist version=""1.0"">
<dict>
	<key>$archiver</key>
	<string>NSKeyedArchiver</string>
	<key>$objects</key>
	<array>
		<string>$null</string>
		<dict>
			<key>$class</key>
			<dict>
				<key>CF$UID</key>
				<integer>3</integer>
			</dict>
			<key>Birth</key>
			<integer>{0}</integer>{1}
			<key>GroupID</key>
			<integer>{2}</integer>
			<key>InodeNumber</key>
			<integer>{3}</integer>
			<key>LastModified</key>
			<integer>{4}</integer>
			<key>LastStatusChange</key>
			<integer>{5}</integer>
			<key>Mode</key>
			<integer>{6}</integer>
			<key>ProtectionClass</key>
			<integer>{7}</integer>
			<key>RelativePath</key>
			<dict>
				<key>CF$UID</key>
				<integer>2</integer>
			</dict>
			<key>Size</key>
			<integer>{8}</integer>
			<key>UserID</key>
			<integer>{9}</integer>
		</dict>
		<string>{10}</string>
		<dict>
			<key>$classes</key>
			<array>
				<string>MBFile</string>
				<string>NSObject</string>
			</array>
			<key>$classname</key>
			<string>MBFile</string>
		</dict>
	</array>
	<key>$top</key>
	<dict>
		<key>root</key>
		<dict>
			<key>CF$UID</key>
			<integer>1</integer>
		</dict>
	</dict>
	<key>$version</key>
	<integer>100000</integer>
</dict>
</plist>
";

            string strFlag = @"
			<key>Flags</key>
			<integer>{0}</integer>";

            string strFormat = string.Format(strPlist,
                Common.ConvertToPhoneTime(record.CreateTime, DateTime.Parse("1970-01-01")),
                (record.FlagsInPlist >= 0 ? string.Format(strFlag, record.FlagsInPlist) : ""),
                record.GroupID,
                record.InodeNumber,
                 Common.ConvertToPhoneTime(record.ModifyTime, DateTime.Parse("1970-01-01")),
                 Common.ConvertToPhoneTime(record.AccessTime, DateTime.Parse("1970-01-01")),
                 record.Mode,
                 (int)record.ProtectionTypeInPlist,
                 record.Size,
                 record.UserID,
                 record.relativePath);

            arrData = CoreFoundation.CreatePlistBinaryData(strFormat);

            return arrData;
        }


        public static MBFileRecord MDM_CreateRecord(string dirBackup, int versionNumber, string domain, string relativePath, ushort mode, MBProtectionType protectionType, int inodeNumber, object objData)
        {
            MBFileRecord item = new MBFileRecord();
            item.domain = domain;
            item.relativePath = relativePath;
            item.InodeNumber = (uint)inodeNumber;
            item.Mode = mode;
            item.UserID = 501;
            item.ProtectionTypeInPlist = protectionType;

            item.fileID = Common.GetSha1Hex(string.Format("{0}-{1}", item.domain + "", item.relativePath + ""));

            string dirSave = dirBackup;
            string sku = string.Empty;
            item.domainType = MBFileRecord.GetDomainType(item.domain, ref sku);

            if (!string.IsNullOrEmpty(item.domain) && !string.IsNullOrEmpty(item.relativePath))
            {
                item.PathOnPhone = mbdb.FormatPath(item.domainType, item.relativePath, sku);

                if (versionNumber >= 1000)
                    dirSave = Path.Combine(dirBackup, item.fileID.Substring(0, 2));

                item.PathOnPhone = mbdb.FormatPath(item.domainType, item.relativePath);
                item.PathOnPC = Path.Combine(dirSave, item.fileID);
            }

            if (objData != null)
            {
                if (objData is byte[])
                {
                    Folder.CheckFolder(dirSave);
                    File.WriteAllBytes(item.PathOnPC, (byte[])objData);
                }
                else if (objData is string)
                {
                    string strFile = (string)objData;
                    if (string.Compare(strFile, item.PathOnPC, true) != 0)
                    {
                        Folder.CheckFolder(dirSave);
                        File.Copy(strFile, item.PathOnPC);
                    }
                }
            }

            if (File.Exists(item.PathOnPC))
            {
                FileInfo info = new FileInfo(item.PathOnPC);
                item.ModifyTime = info.LastWriteTime;
                item.AccessTime = info.LastAccessTime;
                item.CreateTime = info.CreationTime;

                item.Size = info.Length;
                item.flags = 1;
                item.GroupID = -2;

                item.FlagsInPlist = 0;
            }
            else
            {
                item.ModifyTime = DateTime.Now;
                item.AccessTime = DateTime.Now;
                item.CreateTime = DateTime.Now;

                item.Size = 0;
                item.flags = 2;
                item.GroupID = 501;
            }

            item.fileInfoPlist = MDM_CreatePlist_FileInfo(item);

            return item;
        }

        /// <summary>
        /// 创建MDM备份资料
        /// </summary>
        /// <param name="dirBackup">备份资料的目录</param>
        /// <param name="dev">还原的设备</param>
        /// <param name="config">config为空退出监督模式，非空为进入监督模式</param>
        /// <returns></returns>
        public static bool MDM_CreateBackupFiles(string dirBackup, iPhoneDevice dev, MDMConfig config, byte[] arrDbData, BackupRestoreEventArgs args)
        {
            bool blnResult = false;

            try
            {
                Folder.CheckFolder(dirBackup);

                if (args == null)
                    args = new BackupRestoreEventArgs();

                if (!MDM_CreatePlist_Manifest(dirBackup, dev))
                {
                    args.ErrorMsg = string.Format("MDM_CreatePlist_Manifest failed -> path={0}, deviceName={1}", dirBackup, dev.DeviceName);
                    Common.LogException(args.ErrorMsg, "MDM_CreateBackupFiles");
                    goto DoExit;
                }

                if (!MDM_CreatePlist_Status(dirBackup, dev.VersionNumber >= 1000))
                {
                    args.ErrorMsg = string.Format("MDM_CreatePlist_Status failed -> path={0}, deviceName={1}", dirBackup, dev.DeviceName);
                    Common.LogException(args.ErrorMsg, "MDM_CreateBackupFiles");
                    goto DoExit;
                }

                if (!MDM_CreatePlist_Info(dirBackup, dev))
                {
                    args.ErrorMsg = string.Format("MDM_CreatePlist_Info failed -> path={0}, deviceName={1}", dirBackup, dev.DeviceName);
                    Common.LogException(args.ErrorMsg, "MDM_CreateBackupFiles");
                    goto DoExit;
                }

                string strDomain = "SysSharedContainerDomain-systemgroup.com.apple.configurationprofiles";
                int InodeNumber = 0;
                List<MBFileRecord> list = new List<MBFileRecord>();
                list.Add(MDM_CreateRecord(
                  dirBackup,
                  dev.VersionNumber,
                  strDomain,
                  "",
                  16832,
                  MBProtectionType.Normal,
                  ++InodeNumber,
                  null));

                list.Add(MDM_CreateRecord(
                  dirBackup,
                  dev.VersionNumber,
                  strDomain,
                  "Library",
                  16832,
                  MBProtectionType.Normal,
                  ++InodeNumber,
                  null));

                list.Add(MDM_CreateRecord(
                  dirBackup,
                  dev.VersionNumber,
                  strDomain,
                  "Library/ConfigurationProfiles",
                  16832,
                  MBProtectionType.Normal,
                  ++InodeNumber,
                  null));

                list.Add(MDM_CreateRecord(
                    dirBackup, 
                    dev.VersionNumber,
                    strDomain, 
                    "Library/ConfigurationProfiles/CloudConfigurationDetails.plist",
                    33206,
                    MBProtectionType.Normal,
                    ++InodeNumber,
                    MDM_CreatePlist_CloudConfigurationDetails(config)));


                blnResult = SaveDB(dirBackup, list, arrDbData, args);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MDM_CreateBackupFiles");
            }

            DoExit:
            return blnResult;
        }


        public static bool UpdateBackupVersion(string strBackFolder, string strVer)
        {
            bool blnReturn = true;
            //ProductVersion
            //Version
            string strInfo = Path.Combine(strBackFolder, "Info.Plist");
            string strManifest = Path.Combine(strBackFolder, "Manifest.Plist");
            string strStatus = Path.Combine(strBackFolder, "Status.Plist");
            Dictionary<object, object> dict = new Dictionary<object, object>();
            try
            {
                dict = (Dictionary<object, object>)CoreFoundation.ReadPlist_managed(strInfo);
                if (dict.ContainsKey("Product Version"))
                {
                    dict["Product Version"] = strVer;
                    CoreFoundation.WritePlist(dict, strInfo);
                }
            }
            catch (Exception ex)
            {
                blnReturn = false;
                Common.LogException(ex.ToString(), "iTunesBackUp_UpdateBackupVersion_Info");
            }

            try
            {
                dict = (Dictionary<object, object>)CoreFoundation.ReadPlist_managed(strManifest);
                if (dict.ContainsKey("Version"))
                {
                    dict["Version"] = strVer; ;
                    CoreFoundation.WritePlist(dict, strManifest);
                }
            }
            catch (Exception ex)
            {
                blnReturn = false;
                Common.LogException(ex.ToString(), "iTunesBackUp_UpdateBackupVersion_Manifest");
            }
            try
            {
                dict = (Dictionary<object, object>)CoreFoundation.ReadPlist_managed(strStatus);
                if (dict.ContainsKey("Version"))
                {
                    dict["Version"] = "2.4";
                    CoreFoundation.WritePlist(dict, strStatus);
                }
            }
            catch (Exception ex)
            {
                blnReturn = false;
                Common.LogException(ex.ToString(), "iTunesBackUp_UpdateBackupVersion_Status");
            }
            return blnReturn;
        }

    }

    public class MBFileRecordCompare : IComparer<MBFileRecord>
    {
        public int Compare(MBFileRecord x, MBFileRecord y)
        {
            return (int)(x.InodeNumber - y.InodeNumber);
        }
    }


    public class MBFileRecord
    {
        #region MBDB结构体

        public string fileID = string.Empty;

        private string _domain = string.Empty;
        public string domain
        {
            get { return this._domain; }
            set
            {
                this._domain = value;
            }
        }

       

        public MBFileDomainType domainType { get; set; } = MBFileDomainType.None;

        public static MBFileDomainType GetDomainType(string domain)
        {
            string sku = string.Empty;
            return GetDomainType(domain, ref sku);
        }

        public static MBFileDomainType GetDomainType(string domain, ref string sku)
        {
            MBFileDomainType typeReturn = MBFileDomainType.None;

            string[] arrItem = domain.Split(new char[] { '-' }, 2);
            if (arrItem.Length >= 1)
            {
                MBFileDomainType type = MBFileDomainType.None;
                if (System.Enum.TryParse<MBFileDomainType>(arrItem[0], out type))
                    typeReturn = type;
            }

            if (arrItem.Length >= 2)
            {
                switch (typeReturn)
                {
                    case MBFileDomainType.AppDomain:
                        {
                            //AppDomain-com.apple.AppStore
                            sku = arrItem[1].Substring("AppDomain-".Length);
                            break;
                        }

                    case MBFileDomainType.AppDomainGroup:
                        {
                            //AppDomainGroup-group.com.tencent.xin
                            sku = arrItem[1].Substring("AppDomainGroup-group".Length);
                            break;
                        }

                    case MBFileDomainType.AppDomainPlugin:
                        {
                            //SysContainerDomain-com.apple.ClipServices.clipserviced
                            sku = arrItem[1].Substring("SysContainerDomain-".Length);
                            break;
                        }

                    case MBFileDomainType.SysContainerDomain:
                        {
                            //SysContainerDomain-com.apple.ClipServices.clipserviced
                            sku = arrItem[1].Substring("SysContainerDomain-".Length);
                            break;
                        }

                    case MBFileDomainType.SysSharedContainerDomain:
                        {
                            //SysSharedContainerDomain-systemgroup.com.apple.mobilegestaltcache
                            sku = arrItem[1].Substring("SysSharedContainerDomain-systemgroup".Length);
                            break;
                        }
                }
            }

            return typeReturn;
        }

        public static string GetDomainValue(MBFileDomainType domainType, string sku = "")
        {
            string strDomain = string.Empty;

            switch (domainType)
            {
                case MBFileDomainType.AppDomain:
                    {
                        //AppDomain-com.apple.AppStore
                        sku = "AppDomain-" + sku;
                        break;
                    }

                case MBFileDomainType.AppDomainGroup:
                    {
                        //AppDomainGroup-group.com.tencent.xin
                        sku = "AppDomainGroup-group" + sku;
                        break;
                    }

                case MBFileDomainType.AppDomainPlugin:
                    {
                        //SysContainerDomain-com.apple.ClipServices.clipserviced
                        sku = "SysContainerDomain-" + sku;
                        break;
                    }

                case MBFileDomainType.SysContainerDomain:
                    {
                        //SysContainerDomain-com.apple.ClipServices.clipserviced
                        sku = "SysContainerDomain-" + sku;
                        break;
                    }

                case MBFileDomainType.SysSharedContainerDomain:
                    {
                        //SysSharedContainerDomain-systemgroup.com.apple.mobilegestaltcache
                        sku = "SysSharedContainerDomain-systemgroup" + sku;
                        break;
                    }

                case MBFileDomainType.None:
                    break;

                default:
                    strDomain = domainType.ToString();
                    break;
            }

            return strDomain;
        }

        public string relativePath = string.Empty;
        public string LinkTarget = string.Empty;
        public string DataHash = string.Empty;

        private UInt16 mMode;
        public UInt16 Mode
        {
            get
            {
                if (this.mMode == 0)
                {
                    this.mMode = 16877;
                    if (File.Exists(this.PathOnPC))
                    {
                        this.mMode = 33188;
                        this.flags = 3;
                    }
                }
                
                return this.mMode;
            }
            set
            {
                this.mMode = value;
            }
        }

        public string alwaysNull = string.Empty;
        public string data = string.Empty;
        public int alwaysZero;
        public UInt32 InodeNumber;
        public UInt32 UserID;
        public int GroupID;
        public DateTime ModifyTime;
        public DateTime AccessTime;
        public DateTime CreateTime;
        public long Size;

        private byte mflag;
        public byte flags
        {
            get
            {
                if (this.Mode == 33188 && this.mflag == 0 && this.fileInfoPlist != null)
                {
                    this.mflag = 3;
                }
                return this.mflag;
            }
            set
            {
                this.mflag = value;
            }
        }

        public byte PropertyCount;
        public System.Collections.Specialized.NameValueCollection Properties;
        public byte[] fileInfoPlist;

        #endregion

        public MBFileType flagType { get { return (MBFileType)this.FlagsInPlist; } }
        public int FlagsInPlist = -1;
        public MBProtectionType ProtectionTypeInPlist = MBProtectionType.Normal;

        public string PathOnPhone = string.Empty;

        public string BackupFolder = string.Empty;
        public bool HasLoadFromPlist;

        public string file = string.Empty;


        private string mPathOnPC;
        public string PathOnPC
        {
            get
            {
                if (string.IsNullOrEmpty(this.mPathOnPC) && this.fileInfoPlist != null)
                {
					this.mPathOnPC = Path.Combine(this.BackupFolder, this.fileID.Substring(0, 2), this.fileID);
                }

                return this.mPathOnPC;
            }
            set
            {
                this.mPathOnPC = value;
            }
        }

        public string PathOnPC_Decrypt = string.Empty;

        //加载备份文件里面所有信息包括plist里面的信息。用来生成10.X以下的备份文件格式
        public void LoadInfoFromPlist()
        {
            try
            {
#if MAC || IOS
                return;
#endif

                if (this.fileInfoPlist == null)
                    return;

                if (this.HasLoadFromPlist)
                    return;

                this.HasLoadFromPlist = true;

                //Console.WriteLine(Encoding.UTF8.GetString(this.FileInfoPlist));

                //string strPath = Folder.GetDesktopPath("A.plist");
                //File.WriteAllBytes(strPath, this.FileInfoPlist);
                //return;

                string strPlist = CoreFoundation.ReadPlist(this.fileInfoPlist);
                long strMode = Utility.SearchXmlByKeyAsInteger(strPlist, "Mode");
                long strBirth = Utility.SearchXmlByKeyAsInteger(strPlist, "Birth");
                long strLastModified = Utility.SearchXmlByKeyAsInteger(strPlist, "LastModified");
                long strLastStatusChange = Utility.SearchXmlByKeyAsInteger(strPlist, "LastStatusChange");
                long strInodeNumber = Utility.SearchXmlByKeyAsInteger(strPlist, "InodeNumber");
                long strGroupID = Utility.SearchXmlByKeyAsInteger(strPlist, "GroupID");
                long strUserID = Utility.SearchXmlByKeyAsInteger(strPlist, "UserID");                
                long strSize = Utility.SearchXmlByKeyAsInteger(strPlist, "Size");
                

                this.Mode = (ushort)strMode;
                this.CreateTime = Common.ConvertToPcTimeFrom1970(strBirth);
                this.ModifyTime = Common.ConvertToPcTimeFrom1970(strLastModified);
                this.AccessTime = Common.ConvertToPcTimeFrom1970(strLastStatusChange);
                this.InodeNumber = (uint)strInodeNumber;
                this.GroupID = (int)strGroupID;
                this.UserID = (uint)strUserID;
                this.Size = strSize;

                if ((MBFileType)this.flags == MBFileType.File)
                {
                    string strData = Utility.SearchTextByKeyAsString(strPlist, "data").Replace("\t", "");
                    if (!string.IsNullOrEmpty(strData))
                        this.DataHash = Common.ToHexString(Convert.FromBase64String(strData));
                }

                this.fileInfoPlist = null;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MBFileRecord_LoadInfoFromPlist");
            }
        }
    }

    public enum MBFileDomainType
    {
        /// <summary>
        /// 未指定
        /// </summary>
        None,

        /// <summary>
        /// 应用域
        /// 对应单个应用的私有沙盒存储区域，仅所属应用可访问
        /// 典型路径示例：
        /// AppDomain-com.apple.AppStore（App Store应用的私有域）
        /// Library/com.apple.AppleMediaServices（应用的媒体服务配置）
        /// Documents/JSStorageObject.plist（应用的用户数据文件）
        /// </summary>
        AppDomain,

        /// <summary>
        /// 应用分组域
        /// 用于同一开发者的多个关联应用间共享数据的公共区域
        /// 典型路径示例：
        /// AppDomainGroup-group.com.tencent.xin（微信系列应用共享组）
        /// Library/Preferences/group.com.tencent.xin.plist（共享偏好设置）
        /// share/d41d8cd98f00b204e9800998ecf8427e/session/headImg（共享头像资源）
        /// </summary>
        AppDomainGroup,

        /// <summary>
        /// 应用插件域
        /// 应用扩展（如Widget、Today插件）的私有存储区域
        /// 典型路径示例：
        /// AppDomainPlugin-com.tencent.xin.widget（微信插件域）
        /// Library/Preferences（插件的配置数据）
        /// Documents（插件生成的用户数据）
        /// </summary>
        AppDomainPlugin,

        /// <summary>
        /// 系统容器域
        /// 系统进程及核心服务的私有存储容器
        /// 典型路径示例：
        /// SysContainerDomain-com.apple.ClipServices.clipserviced（剪贴板服务容器）
        /// Documents（系统服务的持久化数据）
        /// Library（服务配置文件）
        /// Library/Preferences（服务偏好设置）
        /// tmp（临时工作文件）
        /// </summary>
        SysContainerDomain,

        /// <summary>
        /// 系统共享容器域
        /// 供多个系统组件共享数据的公共容器区域
        /// 典型路径示例：
        /// SysSharedContainerDomain-systemgroup.com.apple.mobilegestaltcache（系统配置缓存组）
        /// Library/ConfigurationProfiles/PublicInfo（共享的配置文件信息）
        /// </summary>
        SysSharedContainerDomain,

        /// <summary>
        /// 相机域
        /// 存储相机胶卷及照片库相关数据
        /// 典型路径示例：
        /// Media/PhotoData（照片库主目录）
        /// Media/PhotoData/CPLAssets（iCloud照片流同步数据）
        /// </summary>
        CameraRollDomain,

        /// <summary>
        /// 数据库域
        /// 系统级核心数据库及配置文件存储区
        /// 典型路径示例：
        /// com.apple.xpc.launchd（系统启动服务配置数据库）
        /// timezone/localtime（时区配置文件）
        /// com.apple.xpc.launchd/disabled.migrated（服务禁用状态记录）
        /// PlugInKit-Annotations（插件注解数据库）
        /// </summary>
        DatabaseDomain,

        /// <summary>
        /// 健康域
        /// 存储健康应用及授权程序的健康数据
        /// 典型路径示例：
        /// locationd（与健康相关的位置信息，如运动轨迹）
        /// </summary>
        HealthDomain,

        /// <summary>
        /// 用户主域
        /// 用户主目录下的公共存储区域，关联文件管理服务
        /// 典型路径示例：
        /// Library/Application Support/FileProvider/com.apple.FileProvider.LocalStorage（本地文件提供者数据）
        /// </summary>
        HomeDomain,

        /// <summary>
        /// 家庭套件域
        /// 存储HomeKit智能家居平台相关数据
        /// 包含内容：智能家居设备配置、场景设置、设备状态记录等
        /// Library/homed
        /// </summary>
        HomeKitDomain,

        /// <summary>
        /// 安装域
        /// 应用安装与更新过程中的临时数据存储区
        /// 包含内容：安装包缓存、校验文件、升级日志、临时解压文件等
        /// Library/MobileInstallation/BackedUpState/SystemAppInstallState.plist
        /// Library/MobileInstallation/BackedUpState/BackupSystemAppInstallState.plist
        /// </summary>
        InstallDomain,

        /// <summary>
        /// 键盘域
        /// 系统及第三方键盘的配置数据存储区
        /// 包含内容：键盘布局、用户词典、输入预测模型、表情缓存等
        /// Library/Keyboard/langlikelihood.dat
        /// Library/Keyboard/emoji_adaptation.db
        /// Library/Keyboard/user_model_database.sqlite
        /// </summary>
        KeyboardDomain,

        /// <summary>
        /// 钥匙串域
        /// 系统钥匙串存储区域，存放敏感凭证信息
        /// 包含内容：账号密码、加密密钥、证书、身份凭证等（均加密存储）
        /// keychain-backup.plist
        /// </summary>
        KeychainDomain,

        /// <summary>
        /// 管理偏好域
        /// 由MDM（移动设备管理）管控的应用偏好设置区
        /// 包含内容：企业级应用配置、受限功能设置、政策管控参数等
        /// mobile/.GlobalPreferences.plist
        /// mobile/com.apple.webcontentfilter.plist
        /// </summary>
        ManagedPreferencesDomain,

        /// <summary>
        /// 媒体域
        /// 系统媒体资源（非相机胶卷）的存储区域
        /// 包含内容：音乐、视频、播客、有声书等媒体文件及元数据
        /// Media/iTunes_Control
        /// Media/Recordings
        /// Library/Recordings/20211102 155458.m4a
        /// </summary>
        MediaDomain,

        /// <summary>
        /// 移动设备域
        /// 设备硬件相关配置与状态数据存储区
        /// 包含内容：设备信息、硬件状态日志、硬件权限配置等
        /// ProvisioningProfiles/mis.db
        /// </summary>
        MobileDeviceDomain,

        /// <summary>
        /// 网络域
        /// 网络相关配置与缓存存储区
        /// 包含内容：网络设置、DNS缓存、VPN配置、Wi-Fi连接记录等
        /// Library/Preferences/com.apple.symptomsd.plist
        /// </summary>
        NetworkDomain,

        /// <summary>
        /// 受保护域
        /// 启用高级加密保护的敏感数据存储区
        /// 特点：需设备解锁才能访问，包含高隐私数据（如支付信息、健康记录）
        /// trustd/private/TransparentConnectionPins.plist
        /// trustd/private/TrustStore.sqlite3
        /// trustd/private/CTExceptions.plist
        /// trustd/private/CARevocation.plist
        /// </summary>
        ProtectedDomain,

        /// <summary>
        /// 根域
        /// 备份文件系统的根目录区域，包含全局共享数据
        /// 作用：类似文件系统根目录，存储跨域的全局配置信息
        /// Library/Preferences/com.apple.locationd.plist
        /// Library/Caches/locationd/gyroCal.db
        /// </summary>
        RootDomain,

        /// <summary>
        /// 系统偏好域
        /// 系统设置（Settings.app）的配置数据存储区
        /// 包含内容：用户对系统功能的偏好设置（亮度、声音、通知等）
        /// SystemConfiguration/com.apple.radios.plist
        /// com.apple.networkextension.control.plist
        /// </summary>
        SystemPreferencesDomain,

        /// <summary>
        /// 铃声域
        /// 系统及用户自定义铃声的存储区域
        /// 包含内容：来电铃声、通知铃声、闹钟铃声等音频文件
        /// Media/iTunes_Control
        /// Media/iTunes_Control/iTunes
        /// Media/Purchases
        /// </summary>
        TonesDomain,

        /// <summary>
        /// 无线域
        /// 无线通信相关配置与状态存储区
        /// 包含内容：蓝牙配对信息、蜂窝网络配置、Wi-Fi密码（加密）、基站信息等
        /// Library/Preferences/com.apple.ipTelephony.plist
        /// Library/Databases/CellularUsage.db
        /// Library/Databases/DataUsage.sqlite
        /// </summary>
        WirelessDomain,
    }

    public enum MBProtectionType : byte
    {
        Link = 0,
        Complete = 1,
        CompleteUnlessOpen = 2,
        AccessAfterFirstUserAuth = 3,
        Normal = 4,
        Recovery = 5
    }

    public enum MBFileType : int
    {
        None = -1,
        File = 1,
        Folder = 2,
        Symlink = 4,
        All = 7
    }


    public class MDMConfig
    {
        public string ConfigurationURL = string.Empty;
        public string OrganizationAddress = string.Empty;
        public string OrganizationEmail = string.Empty;
        public string OrganizationName = string.Empty;
        public string OrganizationPhone = string.Empty;

        public string AccountID = string.Empty;
        public string DeviceID = string.Empty;
        public string PairingCode = string.Empty;
    }

    public class MBFileSearch
    {
        public MBFileDomainType Domain = MBFileDomainType.None;

        public string FileID = string.Empty;
        public string RelativePath = string.Empty;
        public string LikePath = string.Empty; 
    }

    public class MBFileSearchResult
    {
        private StringBuilder sbErrMsg = new StringBuilder();
        public void ErrMsgAppendLine(string msg)
        {
            this.sbErrMsg.AppendLine(msg);
        }

        public MBErrorType Type = MBErrorType.None;
        public string ErrMsg { get { return this.sbErrMsg.ToString(); } }

        public List<MBFileRecord> ListData = new List<MBFileRecord>();
    }

    public enum MBErrorType
    {
        None = 0,
        ManifestPlistNotExist,
        ManifestDbNotExist,
        ManifestDbDecryptError,
        ManifestDbConnectFailed,
        ManifestDbSqlError,
        FileDecryptError,
    }
}
