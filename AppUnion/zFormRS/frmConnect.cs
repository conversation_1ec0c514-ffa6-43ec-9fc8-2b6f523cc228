using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;
using System.Runtime.InteropServices;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
using ViewType = iTong.Android.ViewType;
#else
using System.Drawing;
using Microsoft.Win32;
using iTong.CoreCefSharp;
#endif

namespace iTong.CoreModule
{
    public partial class frmConnect : frmNotify
    {
        private tdActionHelper<tdActionItemForRS> mActionHelper = tdActionHelper<tdActionItemForRS>.Instance();

        /// <summary>
        /// true 为请求连接界面
        /// false为连接成功界面
        /// </summary>
        public bool IsConnectEvent { get; set; } = true;

        public override bool skShowLeftButton => true;

        private skTimer mTimerCount;
        private skTimer mTimerVoice;
        private skTimer mTimerTopMost;

        private bool mOpenVoice = false;
        private bool mNeedSendMsg = true;
        private bool IsNeedTopMost = true;

        public skMsgInfoNew mVoIPMsgInfo;
        public skMsgInfoNew VoIPMsgInfo
        {
            get { return this.mVoIPMsgInfo; }
        }

        private bool mReceVoIP;
        public bool ReceVoIP
        {
            get { return this.mReceVoIP; }
            set { mReceVoIP = value; }
        }

        public frmChat frmChat;

#if MAC
        private int mWindowWidth = 400; //this.Window有默认10px的圆角，影响不规则窗体的绘制，所以默认把窗体宽度增加10px
#else
        private int mWindowWidth = 390;
#endif

        public frmConnect() : base()
        {
            InitializeComponent();
#if MAC
            this.InitFrame();
#else
            this.pnlTarDevice.Location = this.pnlRemoteRequest.Location;
            this.Location = new Point(-10000, -10000);
#endif
        }

#if MAC

        public static void ShowConnect(bool isConnect = true)
        {
            RSEvent rsEvent = RSEvent.Create(isConnect ? RSEventType.Connect : RSEventType.ImageRecvSuccess, "");
            rsEvent.TargetDevice = new skDevice() { unique_id = "6431f0c441f2e852b18e8f7a449fe896", name = "MacOS" };
            rsEvent.TargetDevice.user_info.mail = "<EMAIL>";
            rsEvent.Msg = new skSocketMsgForRS();
            rsEvent.Msg.body = @"{""command"":""remote_support\/connect"",""device_info"":{""name"":""WIN11-GXF-215"",""unique_id"":""6431f0c441f2e852b18e8f7a449fe896"",""local_ip"":""**************"",""use_wifi"":0,""device_type"":23,""app_version"":1241,""support_2d_camera"":1,""display_show_mouse"":0,""display_hide_wallpaper"":0,""display_quality"":2,""display_fps"":-1},""user_info"":{""account_id"":""*********"",""account_type"":2,""avatar"":""https:\/\/dl.airdroid.com\/20180717113241_default.png"",""nickname"":""=#btestfee##="",""mail"":""<EMAIL>"",""token"":""""},""company_info"":{""id"":""*********"",""name"":""未来科技-奇""},""pid"":""23_f1d0fb478b8a9a4f04afecf70db64f17_0""}";
            rsEvent.Msg.bodyRequest = MyJson.DeserializeFromJsonString<skSocketBodyForRS>(rsEvent.Msg.body);

            frmChat.IsDebugMode = true;

            frmConnect frm = NotifyMgr.OpenNotify<frmConnect>(rsEvent, NotifyType.RightBottom, true);
        }

        protected void InitFrame()
        {
            this.skShowButtonClose = false;
            this.skShowButtonMax = false;
            this.skShowButtonMin = false;

            this.skShowStatusBar = false;
            this.skCanResize = false;
            this.skSuperviewReceiveMessage = false;
            this.skTitleAlign = NSTextAlignment.Left;

            this.SizeHandle();

            this.btnDisconnect.Location = new Point(this.Width - 20 - this.btnDisconnect.Width, this.Height - 7 - this.btnDisconnect.Height);
            this.btnDisconnect.Anchor = AnchorStyles.Right | AnchorStyles.Top;
            this.pnlRemoteRequest.Location = new Point(30, 10);
            this.pnlTarDevice.Location = new Point(30, this.Height - 36 - this.pnlTarDevice.Height);// this.pnlRemoteRequest.Location;
            //this.pnlTarDevice.Size = new Size(this.Width - this.pnlTarDevice.Left, this.Height - this.skTitleBarHeight - 10);
            this.pnlTarDevice.Anchor = this.pnlRemoteRequest.Anchor;

            //请求连接面板
            this.picRequestImage.Location = new Point(20, this.pnlRemoteRequest.Height - 24 - this.picRequestImage.Height);
            this.lblRequestName.Location = new Point(64, this.pnlRemoteRequest.Height - 22 - this.lblRequestName.Height);
            this.lblRequestDetail.Location = new Point(64, this.pnlRemoteRequest.Height - 43 - this.lblRequestDetail.Height);

            this.pnlRemoteRequestAllow.Location = new Point(1, this.pnlRemoteRequest.Height - 84 - this.pnlRemoteRequestAllow.Height);
            this.btnActionConnect.Location = new Point(63, this.pnlRemoteRequestAllow.Height - 3 - this.btnAction.Height);

            var chkRemoteRequestFrame = this.chkRemoteRequest.Frame;
            this.chkRemoteRequest.Frame = new Rectangle(60, this.pnlRemoteRequestAllow.Height - 26 - (int)chkRemoteRequestFrame.Height, (int)chkRemoteRequestFrame.Width, (int)chkRemoteRequestFrame.Height);

            this.lblTip.Location = new Point(38, this.pnlRemoteRequest.Height - 161 - this.lblTip.Height);
            this.picTip.Location = new Point(this.lblTip.Left - 2 - this.picTip.Width, this.lblTip.Top + (this.lblTip.Height - this.picTip.Height) / 2 + 2);

            this.btnReject.Location = new Point(20, this.pnlRemoteRequest.Height - 190 - this.btnReject.Height);
            this.btnAgree.Location = new Point(192, this.pnlRemoteRequest.Height - 190 - this.btnAgree.Height);

            //连接成功面板
            //this.pnlTarDevice.skBackgroundColor = Color.Red;
            //this.pnlTarDeviceInfo.skBackgroundColor = Color.Blue;
            //this.pnlTarDeviceInfoAllow.skBackgroundColor = Color.Brown;
            //this.pnlTarDeviceInfo.Anchor = AnchorStyles.Left | AnchorStyles.Top;

            this.btnVoiceContent.Location = new Point(0, this.pnlTarDevice.Height - 1 - this.btnVoiceContent.Height);
            this.pnlTarDeviceInfo.Location = new Point(0, 0);

            this.picUserImage.Location = new Point(20, this.pnlTarDeviceInfo.Height - 24 - this.picUserImage.Height);
            this.lblUserName.Location = new Point(64, this.pnlTarDeviceInfo.Height - 22 - this.lblUserName.Height);
            this.lblUserDetail.Location = new Point(64, this.pnlTarDeviceInfo.Height - 43 - this.lblUserDetail.Height);

            this.pnlSplite.Location = new Point(289, this.pnlTarDeviceInfo.Height - 27 - this.pnlSplite.Height);
            this.btnVoice.Location = new Point(297, this.pnlTarDeviceInfo.Height - 26 - this.btnVoice.Height);
            this.btnChat.Location = new Point(329, this.pnlTarDeviceInfo.Height - 26 - this.btnChat.Height);

            this.pnlTarDeviceInfoAllow.Location = new Point(0, 0);
            this.btnAction.Location = new Point(18, this.pnlTarDeviceInfoAllow.Height - 15 - this.btnAction.Height);

            var chkRemoteControlFrame = this.chkRemoteControl.Frame;
            this.chkRemoteControl.Frame = new Rectangle(16, this.pnlTarDeviceInfoAllow.Height - 42 - (int)chkRemoteControlFrame.Height, (int)chkRemoteControlFrame.Width, (int)chkRemoteControlFrame.Height);
            this.chkFileTransfer.Location = new Point(171, this.pnlTarDeviceInfoAllow.Height - 42 - this.chkFileTransfer.Height);

            //this.pnlTarDevice.skFlowDirection = FlowDirection.TopDown;
            //this.pnlTarDevice.skShowHorizontalBar = false;
            //this.pnlTarDevice.skShowVerticalBar = false;
            this.pnlTarDevice.IsSuspendLayout = false;
            this.pnlTarDevice.AutoLayout();

            // 原生 NSButton 不需要设置图标偏移
            this.chkFileTransfer.skIconOffset = new Point(0, -0.3);
            this.btnAction.skIconOffset = new Point(0, -0.8);
        }
#endif


#if DPI
        protected override void OnDpiChanged()
        {
            base.OnDpiChanged();
            this.skTransparentImagePaddingForNormal = new Padding(7, 8, 8, 8).ToDPI(this.Dpi);
            this.skTransparentImagePaddingForMini = new Padding(8, 7, 0, 7).ToDPI(this.Dpi);
            this.SizeHandle(true);

        }
#endif

        //private void SystemEvents_DisplaySettingsChanged(object sender, EventArgs e)
        //{
        //    this.SizeHandle(true);
        //}

        public override void SetRsEvent(RSEvent e)
        {
            base.SetRsEvent(e);

            this.IsConnectEvent = e.EventType == RSEventType.Connect;

            if (!this.IsConnectEvent)
            {
                this.skAutoPackup = true;
                this.skAutoPackupInterval = 3000;
            }
        }

        public override void SetUI()
        {
            base.SetUI();

            this.SetControlState();
            this.SizeHandle();
        }

#if MAC
        public override void SetBtnDisconnectVisible()
        {
            if (this.IsConnectEvent)
                return;

            this.btnDisconnect.Visible = !this.skIsMiniMode;

            // 在迷你模式切换时，确保btnAllow按钮正确设置
            if (this.skIsMiniMode)
            {
                this.BeginInvoke(new Action(() => {
                    this.AdjustLeftButtonForMiniMode();
                }));
            }
        }

        /// <summary>
        /// 重写RefreshRegion方法，修复frmConnect的迷你模式点击问题
        /// 保持不规则形状，但确保btnAllow按钮能够正确接收点击事件
        /// </summary>
        public override void RefreshRegion()
        {
            // 调用父类方法，保持原有的不规则形状（包括迷你模式的灰色箭头形状）
            base.RefreshRegion();

            // 在迷你模式下，确保btnAllow按钮能够接收点击事件
            if (this.skIsMiniMode)
            {
                // 延迟调整按钮位置，确保控件已经初始化完成
                this.BeginInvoke(new Action(() => {
                    this.AdjustLeftButtonForMiniMode();
                }));
            }
        }

        /// <summary>
        /// 调整迷你模式下的左侧按钮，确保它能够接收点击事件
        /// </summary>
        private void AdjustLeftButtonForMiniMode()
        {
            // 通过名称查找btnAllow控件（在frmNotify中名称为"btnClick"）
            Control[] controls = this.Controls.Find("btnClick", false);
            if (controls.Length > 0)
            {
                skButton btnAllow = controls[0] as skButton;
                if (btnAllow != null)
                {
                    // 让按钮覆盖整个窗体区域，确保可点击
                    btnAllow.Location = new Point(0, 0);
                    btnAllow.Size = this.Size;
                    btnAllow.BringToFront();

                    // 确保按钮可见且启用
                    btnAllow.Visible = true;
                    btnAllow.Enabled = true;

                    // 设置按钮背景为透明，不影响视觉效果
                    btnAllow.skBackgroundColor = skColor.Transparent;
                }
            }
        }

        /// <summary>
        /// 重写鼠标按下事件，确保迷你模式下的点击正常工作
        /// </summary>
        protected override void BtnAllow_MouseDown(object sender, MouseEventArgs e)
        {
            // 在迷你模式下，简化鼠标处理逻辑，避免拖拽干扰点击
            if (this.skIsMiniMode)
            {
                // 迷你模式下不处理拖拽，只处理点击
                return;
            }

            // 非迷你模式使用父类的拖拽逻辑
            base.BtnAllow_MouseDown(sender, e);
        }

        /// <summary>
        /// 重写鼠标移动事件，确保迷你模式下不会误触发拖拽
        /// </summary>
        protected override void BtnAllow_MouseMove(object sender, MouseEventArgs e)
        {
            // 在迷你模式下，禁用拖拽功能，避免干扰点击
            if (this.skIsMiniMode)
            {
                return;
            }

            // 非迷你模式使用父类的拖拽逻辑
            base.BtnAllow_MouseMove(sender, e);
        }

        /// <summary>
        /// 重写鼠标释放事件
        /// </summary>
        protected override void BtnAllow_MouseUp(object sender, MouseEventArgs e)
        {
            // 调用父类方法
            base.BtnAllow_MouseUp(sender, e);
        }
#endif

        protected override void InitControls()
        {
            base.InitControls();

            this.pnlRemoteRequest.Visible = this.IsConnectEvent;
            this.pnlTarDevice.Visible = !this.IsConnectEvent;
            this.btnDisconnect.Visible = !this.IsConnectEvent;

            int timeout = 60;
            if (!this.IsConnectEvent)
            {
                this.lblUserName.skText = this.TargetDevice?.user_info.mail;
                this.lblUserDetail.skText = this.TargetDevice?.company_info.name;
            }
            else
            {
                this.lblRequestName.skText = this.TargetDevice?.user_info.mail;

                this.mTimerCount = TimerMgr.Create(1, this.OnTimerCount, "", timeout);
            }

            this.skCanbeMove = true;

            this.Icon = MyResource.GetIcon("airdroid.ico");
#if MAC
            this.skIcon = MyResource.GetImage("frmConnect_logo48.png"); 
            this.skIconSize = new Size(22, 22);
            this.skIconPadding = new Padding(50, 0, 0, 0);
#else
            this.skIcon = MyResource.GetImage("logo_rs_2x.png");
            this.skIconSize = new Size(22, 22);
            this.skIconPadding = new Padding(38, 0, 0, 0);
#endif

            this.TopMost = true;
            this.skShowTitle = true;
            this.skTitle = this.Language.GetString("pc_rs_intro_title");
#if !MAC
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
#endif
            this.skTitleColor = skColor.FromArgb(45, 47, 51);
            this.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);

            this.skTitleBarHeight = 36;

            this.skBackgroundColor = Color.White;
            this.skBorderType = skBorderType.None;

            this.lblRequestDetail.skText = this.Language.GetString("rs_connection_device_agree");
            this.lblTip.skText = this.Language.GetString("rs_make_sure_trust");
            this.btnAgree.skText = this.Language.GetString("common_accept");
            this.btnReject.skText = string.Format(this.Language.GetString("rs_pc_reject"), timeout);

            this.btnAction.skText = this.Language.GetString("rs_allow_access");
#if MAC
            this.chkRemoteControl.Title = this.Language.GetString("Device_Module_AirMirror");
            this.chkRemoteRequest.Title = this.Language.GetString("Device_Module_AirMirror");
#else
            this.chkRemoteControl.skText = this.Language.GetString("Device_Module_AirMirror");
            this.chkRemoteRequest.skText = this.Language.GetString("Device_Module_AirMirror");
#endif

            this.btnActionConnect.skText = this.Language.GetString("rs_allow_access");
            this.chkFileTransfer.skText = this.Language.GetString("File.Label.FileTransfer");

            this.btnDisconnect.skToolTip = this.Language.GetString("Transfer_Nearby_Disconnect_Title");
            this.btnVoice.skToolTip = this.Language.GetString("RemoteSupport.Button.Voip");
            this.btnChat.skToolTip = this.Language.GetString("rs_chat");

            if (!this.IsConnectEvent)
                SocketMgr.RsCallback += OnSocketCallback;

            // 订阅取消和开启置顶事件
            this.btnDisconnect.MouseEnter += CloseTopMostWithMouseEnter;
            this.lblUserName.MouseEnter += CloseTopMostWithMouseEnter;
            this.lblUserDetail.MouseEnter += CloseTopMostWithMouseEnter;
            this.btnVoice.MouseEnter += CloseTopMostWithMouseEnter;
            this.btnChat.MouseEnter += CloseTopMostWithMouseEnter;

            this.btnChat.MouseLeave += OpenTopMostWithMouseLeave;
            this.lblUserName.MouseLeave += OpenTopMostWithMouseLeave;
            this.btnDisconnect.MouseLeave += OpenTopMostWithMouseLeave;
            this.lblUserDetail.MouseLeave += OpenTopMostWithMouseLeave;
            this.btnVoice.MouseLeave += OpenTopMostWithMouseLeave;

            // 置顶定时器初始化
            this.mTimerTopMost = TimerMgr.Create(1, this.OnTimerTopMost, delayStart: 1);
            //ControlHandle(this.pnlRemoteRequest);
            //ControlHandle(this.pnlTarDevice);
            //this.pnlRemoteRequest.Size = new Size(MyDpi.DpiHandle(this.pnlRemoteRequest.Size.Width), MyDpi.DpiHandle(this.pnlRemoteRequest.Size.Height));
            this.pnlTarDevice.Size = new Size(this.pnlRemoteRequest.Size.Width, MyDpi.ToDPI(this.pnlTarDevice.Size.Height, this.Dpi));
            //this.pnlTarDevice.Location = new Point(MyDpi.DpiHandle(pnlTarDevice.Location.X), MyDpi.DpiHandle(pnlTarDevice.Location.Y));
            this.pnlTarDevice.MaximumSize = new Size(this.pnlTarDevice.Size.Width, 0);
            //ControlHandleForPanel(this.pnlTarDevice);
            //ControlHandleForPanel(this.pnlRemoteRequest);
            //ControlHandle(this.btnDisconnect);
        }

        //private void ControlHandleForPanel(skPanel panel)
        //{
        //    foreach (Control item in panel.Controls)
        //    {
        //        ControlHandle(item);

        //        if (item is skPanel pnl)
        //        {
        //            ControlHandleForPanel(pnl);
        //        }
        //    }
        //}

        //private void ControlHandle(Control control)
        //{
        //    control.Size = new Size(MyDpi.DpiHandle(control.Size.Width), MyDpi.DpiHandle(control.Size.Height));
        //    control.Location = new Point(MyDpi.DpiHandle(control.Location.X), MyDpi.DpiHandle(control.Location.Y));
        //}

        private void OnSocketCallback(object sender, RSEvent e)
        {
            try
            {
                if (!e.IsRequest && e.EventType != RSEventType.VoIP)
                    return;

                switch (e.EventType)
                {
                    case RSEventType.File:
                    case RSEventType.Text:
                        if (this.frmChat.ShowInTaskbar == false)
                            this.btnChat.skIcon = MyResource.GetImage("rs_msg_new_4.png");
                        break;
                    case RSEventType.VoIP:

                        if (e.TargetDevice.VoIPStatus == skVoIPStatus.Cancel ||
                           e.TargetDevice.VoIPStatus == skVoIPStatus.Refuse ||
                           e.TargetDevice.VoIPStatus == skVoIPStatus.Timeout ||
                           e.TargetDevice.VoIPStatus == skVoIPStatus.PermissionDenied ||
                           e.TargetDevice.VoIPStatus == skVoIPStatus.Finish ||
                           e.TargetDevice.VoIPStatus == skVoIPStatus.Accept)
                        {

                            if (e.TargetDevice.VoIPStatus == skVoIPStatus.Timeout || e.TargetDevice.VoIPStatus == skVoIPStatus.PermissionDenied || e.TargetDevice.VoIPStatus == skVoIPStatus.Finish)
                            {
                                if (this.mVoIPMsgInfo == null && e.TargetDevice.VoIPStatus == skVoIPStatus.Timeout)
                                {
                                    return;
                                }
                                //if (e.TargetDevice.VoIPStatus == skVoIPStatus.Timeout || e.TargetDevice.VoIPStatus == skVoIPStatus.PermissionDenied)
                                //{
                                this.mNeedSendMsg = false;
                                this.VoIPMsgBoxClose();
                                //}

                                this.VoIPMsgHandle(e.TargetDevice.VoIPStatus);
                            }

                            if ((e.TargetDevice.VoIPStatus == skVoIPStatus.Refuse || e.TargetDevice.VoIPStatus == skVoIPStatus.Accept) && this.VoIPMsgInfo != null && this.mReceVoIP == false)
                            {
                                this.mNeedSendMsg = false;
                                this.VoIPMsgBoxClose();

                                if (e.TargetDevice.VoIPStatus == skVoIPStatus.Refuse)
                                {
                                    this.VoIPMsgHandle(skVoIPStatus.Refuse);
                                    this.VoiceHandle(false);
                                }
                                else if (e.TargetDevice.VoIPStatus == skVoIPStatus.Accept)
                                {
                                    RSEvent arg = new RSEvent();
                                    arg.EventType = RSEventType.OpenVoIP;
                                    arg.TargetDevice = e.TargetDevice;
                                    SocketMgr.SendMsgFromClient(arg);
                                    this.VoiceHandle(true);
                                    this.CommonTimeTip(RecordTipType.VoIPOpen);
                                }
                            }
                            else

                            if (e.TargetDevice.VoIPStatus != skVoIPStatus.Accept)
                                this.VoiceHandle(false);
                        }

                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "OnSocketCallback");
            }
        }

        private delegate void VoIPMsgHandleDelegate(skVoIPStatus status);
        public void VoIPMsgHandle(skVoIPStatus status)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new VoIPMsgHandleDelegate(VoIPMsgHandle), status);
                return;
            }

            try
            {
                if (status == skVoIPStatus.Refuse)
                {
                    this.CommonTimeTip(RecordTipType.VoIPRefuse);
                    skMsgInfoNew msg = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_partner_rejected_voice_request")), this.Language.GetString("pc_rs_intro_title"), this.Language);
                    msg.Buttons = MessageBoxButtons.OK;
                    msg.FirstButton.skText = this.Language.GetString("Common_gotit_tip");
                    msg.FirstButton.skBackgroundImage = MyResource.GetImage("btn_blue_4.png");
                    msg.TopMost = true;
                    msg.FormDialog = false;
                    MsgBoxMgr.Show(null, msg);
                }
                else if (status == skVoIPStatus.Timeout)
                {
                    this.CommonTimeTip(RecordTipType.VoIPTimeout);
                    skMsgInfoNew msg = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_voice_connection_timed_out")), this.Language.GetString("pc_rs_intro_title"), this.Language);
                    msg.FirstButton.skText = this.Language.GetString("Common.Retry");
                    msg.SecondButton.skText = this.Language.GetString("Common_gotit_tip");
                    msg.FirstButton.Click = ShowVoIPRequest;
                    msg.TopMost = true;
                    msg.FormDialog = false;
                    msg.MessageInfo.skWordWrapping = true;

                    MsgBoxMgr.Show(null, msg);
                }
                else if (status == skVoIPStatus.PermissionDenied)
                {
                    this.ConnectMsgHandle(this.Language.GetString("rs_pc_not_granted_microphone_permissions"));
                }
                else if (status == skVoIPStatus.Finish)
                {
                    long dur = (long)this.mTimerVoice.TimePass;
                    this.CommonTimeTip(RecordTipType.VoIPEnd, string.Format(this.Language.GetString("RemoteSupport.Message.VoIPFinished"), Utility.FormatDuration(dur, true, true).ToString()), dur);
                    //skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(, this.Language.GetString("pc_rs_intro_title"), this.Language);
                    //skMsgInfo.FirstButton.ButtonVisibily = false;
                    //skMsgInfo.SecondButton.ButtonText = this.Language.GetString("Common_gotit_tip");
                    //skMsgInfo.SecondButton.ButtonFont = MyFont.CreateFont("微软雅黑", 9.75f);
                    //skMsgInfo.SecondButton.ButtonTextColor = Color.White;
                    //skMsgInfo.SecondButton.ButtonBackgroudImage = MyResource.GetImage("btn_blue_4;
                    ////skMsgInfo.SecondButton.ButtonCheckTime = 5;
                    //skMsgInfo.TopMost = true;
                    //skMsgInfo.FormDialog = false;
                    //MsgBoxMgr.Show(null, skMsgInfo);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "VoIPMsgHandle");
            }
        }


        public void CommonTimeTip(RecordTipType tipType, string msg = null, long durtimer = 0)
        {
            try
            {
                if (this.frmChat != null)
                {
                    if (msg == null)
                        this.frmChat.AddCommonTimeTip(tipType);
                    else if (durtimer == 0)
                        this.frmChat.AddCommonTimeTipByMsg(tipType, msg);
                    else
                        this.frmChat.AddCommonTimeTipByMsg(tipType, msg, durtimer);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CommonTimeTip");
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            this.SetUI();

            if (!IsConnectEvent)
            {
#if MAC
                this.chkRemoteControl.Activated += this.chkRemoteControl_CheckedChanged;
#else
                this.chkRemoteControl.CheckedChanged += this.chkRemoteControl_CheckedChanged;
#endif
            }
            else
            {
#if MAC
                this.chkRemoteRequest.Activated += ChkRemoteRequest_CheckedChanged;
#else
                this.chkRemoteRequest.CheckedChanged += ChkRemoteRequest_CheckedChanged;
#endif
            }
            this.InitChat();
        }

        private void ChkRemoteRequest_CheckedChanged(object sender, EventArgs e)
        {
#if MAC
            this.mActionHelper.Add(this.chkRemoteRequest.State == NSCellStateValue.On ? tdActionModeKeyForRS.ConnectControlSelect : tdActionModeKeyForRS.ConnectControlDisselect);
#else
            this.mActionHelper.Add(this.chkRemoteRequest.skChecked ? tdActionModeKeyForRS.ConnectControlSelect : tdActionModeKeyForRS.ConnectControlDisselect);
#endif
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            //SystemEvents.DisplaySettingsChanged -= SystemEvents_DisplaySettingsChanged;

            TimerMgr.Dispose(this.mTimerCount);
            TimerMgr.Dispose(this.mTimerVoice);
            TimerMgr.Dispose(this.mTimerTopMost);

            this.mTimerCount = null;
            this.mTimerVoice = null;
            this.mTimerTopMost = null;

            if (this.mVoIPMsgInfo != null)
                MsgBoxMgr.Remove(this.mVoIPMsgInfo);

            if (this.IsConnectEvent == false)
                SocketMgr.RsCallback -= OnSocketCallback;

            if (this.frmChat != null)
            {
                this.frmChat.CanClose = true;
                this.frmChat.Close();
            }
        }

        private void InitChat()
        {
            if (!this.IsConnectEvent)
            {
                if (this.frmChat == null)
                {
                    bool hasShowVoiceTip = SettingMgr.GetValue<bool>(KeyNameForRS.HasShowVoiceTip, false);
                    this.frmChat = new frmChat(this.TargetDevice, PageHelper.PathRSChat, hasShowVoiceTip);
                    this.frmChat.Shown += frmChat_Shown;
                    this.frmChat.Callback = ChatCallback;
                }
                this.frmChat.StartPosition = FormStartPosition.Manual;

#if MAC
                this.frmChat.LocationForWindow = new Point(-10000, -10000);
                this.frmChat.InitControlsEx();
                this.frmChat.ShowInTaskbar = false;
#else
                this.frmChat.Location = new Point(-10000, -10000);
                this.frmChat.Show();
                this.frmChat.ShowInTaskbar = false;
#endif
            }
        }

        private void frmChat_Shown(object sender, EventArgs e)
        {
#if MAC
            this.frmChat.SetTitle(this.TargetDevice?.user_info.mail);
#else
            this.frmChat.skTitle = this.TargetDevice?.user_info.mail;
#endif

            this.frmChat.SetBtnVoIPHandle(RSEvent.TargetDevice.VoiceEnable);
        }

        private void ChatCallback(object objValue)
        {
            if (objValue is skVoIPStatus)
            {
                switch ((skVoIPStatus)objValue)
                {
                    case skVoIPStatus.None:
                        break;
                    case skVoIPStatus.Call:
                        this.btnVoice_Click(this.btnVoice, null);
                        break;
                    case skVoIPStatus.Cancel:
                        this.VoIPMsgBoxClose();
                        break;
                    case skVoIPStatus.Accept:
                        break;
                    case skVoIPStatus.Refuse:
                        break;
                    case skVoIPStatus.Finish:
                        this.btnVoice_Click(this.btnVoice, null);
                        break;
                }
            }
            else if (objValue is RecordTipType)
            {
                switch ((RecordTipType)objValue)
                {
                    case RecordTipType.Voice:
                        SettingMgr.SetValue(KeyNameForRS.HasShowVoiceTip, true);
                        break;
                }
            }
            else if (objValue is string)
            {
                switch ((string)objValue)
                {
                    case "Paste":
                        {
                            //skMsgInfoNew msg = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_pasted_content_too_long")), this.Language.GetString("pc_rs_intro_title"), this.Language);
                            //msg.FormDialog = false;
                            //msg.TopMost = true;
                            //msg.EventCallBack += () =>
                            //{
                            //    this.frmChat.PasteHandle();
                            //    MsgBoxMgr.RemoveForm(msg);
                            //};

                            //MsgBoxMgr.Show(null, msg);
                        }
                        break;
                }
            }
        }

        public void ShowChat(bool linkVoIP = false)
        {
            try
            {
                //true 为请求连接界面
                if (this.IsConnectEvent)
                {
                    LogFile("ShowChat -> IsConnectEvent = true");
                    return;
                }

                if (this.frmChat == null)
                {
                    LogFile("ShowChat -> this.frmChat == null");
                    InitChat();
                }
                else if (this.frmChat.IsDisposed)
                {
                    this.frmChat = null;
                    LogFile("ShowChat -> this.frmChat.IsDisposed");
                    InitChat();
                }


                if (linkVoIP)
                    this.frmChat.btnVoIP_Click(null, EventArgs.Empty);
                else
                    this.btnChat.skIcon = MyResource.GetImage("rs_msg_4.png");

                this.StopNotifyTimer();

                this.frmChat.Show();
                this.frmChat.WindowState = FormWindowState.Normal;
                this.frmChat.Activate();
                this.frmChat.ShowInTaskbar = true;

#if MAC
                Rectangle rect = NSScreen.MainScreen.VisibleFrame;
                this.frmChat.LocationForWindow = new Point(rect.Left + (rect.Width - this.frmChat.Width) / 2, rect.Top + (rect.Height - this.frmChat.Height) / 2);
#else
                Rectangle rect = Screen.PrimaryScreen.WorkingArea;
                this.frmChat.Location = new Point(rect.Left + (rect.Width - this.frmChat.Width) / 2, rect.Top + (rect.Height - this.frmChat.Height) / 2);
#endif


            }
            catch (Exception ex)
            {
                LogFile(ex.ToString());
            }
        }

        public void LogFile(string msg)
        {
            MyLog.LogFile(msg, "Web");
        }

        public void StopNotifyTimer()
        {
            SocketHandler socket = SocketMgr.GetSocket(SocketType.UserDisplay);
            SocketMgr.RecvMsgOnClient(socket, RSEvent.Create(RSEventType.StopNotifyTimer));
        }

        private void OnTimerCount()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(() =>
                {
                    this.OnTimerCount();
                }));
            }
            else
            {
                this.SetButtonText();

                if (this.mTimerCount.CurrentLoop == this.mTimerCount.MaxLoop)
                {
                    this.RSEvent.EventResult = ((int)skConnectCode.Timeout).ToString();
                    SocketMgr.SendMsgFromClient(this.RSEvent);
                    this.CloseForm();
                }
            }
        }

        private void SetButtonText()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(() =>
                {
                    this.SetButtonText();
                }));
            }
            else
            {
                this.btnReject.skText = string.Format(this.Language.GetString("rs_pc_reject"), this.mTimerCount.MaxLoop - this.mTimerCount.CurrentLoop);
            }
        }

        public void btnAgree_Click(object sender, EventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler(this.btnAgree_Click), sender, e);
            }
            else
            {
                try
                {
                    RsCommon.CloseConnectHandle(this.Handle);

                    this.mActionHelper.Add(tdActionModeKeyForRS.ConnectAccept);

                    this.RSEvent.EventResult = ((int)skConnectCode.Accept).ToString();
#if MAC
                    this.RSEvent.TargetDevice.ControlEnable = this.chkRemoteRequest.Enabled && this.chkRemoteRequest.State == NSCellStateValue.On;
#else
                    this.RSEvent.TargetDevice.ControlEnable = this.chkRemoteRequest.Enabled && this.chkRemoteRequest.skChecked;
#endif

                    skSocketBodyForRS body = this.RSEvent.Msg.bodyRequestGet();
                    if (!(body == null || body.user_info == null))
                    {
                        this.RSEvent.TargetDevice.VoiceEnable = body.user_info.account_type > 0;
                    }

                    SocketMgr.SendMsgFromClient(this.RSEvent);

                    this.CloseForm();
                }
                catch (Exception ex)
                {
                    LogFile(ex.ToString());
                }
            }
        }

        private void btnReject_Click(object sender, EventArgs e)
        {
            this.mActionHelper.Add(tdActionModeKeyForRS.ConnectReject);

            this.RSEvent.EventResult = ((int)skConnectCode.Refuse).ToString();

            SocketMgr.SendMsgFromClient(this.RSEvent);

            this.CloseForm();
        }

        private void btnAction_Click(object sender, EventArgs e)
        {
#if MAC
            if (chkRemoteControl.Hidden == true)
            {
                this.chkRemoteControl.Hidden = false;
                this.btnAction.skIcon = MyResource.GetImage("rs_arrow_up.png");
            }
            else
            {
                this.chkRemoteControl.Hidden = true;
                this.btnAction.skIcon = MyResource.GetImage("rs_arrow_down.png");
            }
#else
            if (chkRemoteControl.Visible == false)
            {
                this.chkRemoteControl.Visible = true;
                this.btnAction.skIcon = MyResource.GetImage("rs_arrow_up.png");
            }
            else
            {
                this.chkRemoteControl.Visible = false;
                this.btnAction.skIcon = MyResource.GetImage("rs_arrow_down.png");
            }
#endif

            this.SizeHandle();
        }

        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            skMsgInfoNew skMsg = RsCommon.GetDefaultMsgInfo(this.Language.GetString("rs_connected_content"), this.Language.GetString("pc_rs_intro_title"), this.Language);
            skMsg.PictureInfo.Image = MyResource.GetImage("rs_pic_disconnect_Icon.png");
            skMsg.PictureInfo.Size = new Size(56, 56);
            skMsg.PictureInfo.Padding = new Padding(-8, 6, 0, 0);

            skMsg.MessageInfo.Padding = new Padding(0, 0, 0, 30);

            skMsg.TopMost = true;
            if (MsgBoxMgr.Show(null, skMsg) == DialogResult.OK)
                this.DisconnectMsgBoxHandle();
        }

        /// <summary>
        /// 断开投屏
        /// </summary>
        private void DisconnectMsgBoxHandle()
        {
            RsCommon.ResetKeyboard();

            this.mActionHelper.Add(tdActionModeKeyForRS.ConnectDisconnect);

            this.CommonTimeTip(RecordTipType.ConnectEnd);

            this.StopNotifyTimer();

            //RSEvent closeSafe = new RSEvent();
            //closeSafe.EventType = RSEventType.SafeModeStatusClose;
            //closeSafe.TargetDevice = this.TargetDevice;
            //SocketMgr.SendMsgFromClient(closeSafe);

            //Thread.Sleep(1000);

            RSEvent arg = new RSEvent();
            arg.EventType = RSEventType.Disconnect;
            arg.TargetDevice = this.TargetDevice;
            SocketMgr.SendMsgFromClient(arg);
            this.CloseForm();
        }

        private void btnVoice_Click(object sender, EventArgs e)
        {
            if (this.mTimerVoice != null)
            {
                skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(this.Language.GetString("rs_voipcall_hangup_title"), this.Language.GetString("pc_rs_intro_title"), this.Language);

                skMsgInfo.PictureInfo.Image = MyResource.GetImage("rs_pic_disconnect_Icon.png");
                skMsgInfo.PictureInfo.Size = new Size(56, 56);
                skMsgInfo.PictureInfo.Padding = new Padding(-8, 6, 0, 0);

                skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 8);

                skMsgInfo.CheckBoxInfo.skIcon = MyResource.GetImage("rs_chk.png");
                skMsgInfo.CheckBoxInfo.skIconSize = new Size(14,14);
                skMsgInfo.CheckBoxInfo.skIconCheck = MyResource.GetImage("rs_chk_check.png");
                skMsgInfo.CheckBoxInfo.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
                skMsgInfo.CheckBoxInfo.skTextColor = skColor.FromArgb(102, 107, 117);
                skMsgInfo.CheckBoxInfo.skText = this.Language.GetString("rs_voip_dlg_tip");
                skMsgInfo.CheckBoxInfo.Padding = new Padding(0, 0, 0, 18);

                if (MsgBoxMgr.Show(null, skMsgInfo) == DialogResult.OK)
                {
                    this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPClose);
                    long dur = (long)this.mTimerVoice.TimePass;
                    this.CommonTimeTip(RecordTipType.VoIPEnd, string.Format(this.Language.GetString("RemoteSupport.Message.VoIPFinished"), Utility.FormatDuration(dur, true, true).ToString()), dur);
                    this.SendVoIP(this.TargetDevice, skVoIPStatus.Finish);

                    if (skMsgInfo.CheckBoxInfo.skChecked)
                        this.DisconnectMsgBoxHandle();
                    else
                        this.VoiceHandle(false);
                }
            }
            else
            {
                List<frmVoiceMsg> frms = MyForm.GetOpenForm<frmVoiceMsg>();

                if (frms.Count > 0)
                {
                    this.ConnectMsgHandle(this.Language.GetString("rs_decvice_in_voice"));
                    return;
                }
#if MAC
                // 强制重新检测麦克风状态
                bool microphoneAvailable = WaveInterop.ForceCheckMicrophoneAvailability();
                this.LogFile(string.Format("btnVoice_Click: ForceCheckMicrophoneAvailability returned {0}", microphoneAvailable));
                
                if (!microphoneAvailable)
                {
                    //请插入麦克风
                    this.ConnectMsgHandle(this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                    return;
                }
#else
                if (WaveInterop.IsDisabledMicrophone())
                {
                    //请插入麦克风
                    this.ConnectMsgHandle(this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                    return;
                }

                if (!WaveInterop.HasMicrophone())
                {
                    //请插入麦克风
                    this.ConnectMsgHandle(this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                    return;
                }
#endif

            if (this.frmChat != null && this.frmChat.IsWaveInterop())
                {
                    this.ConnectMsgHandle(this.Language.GetString("RemoteSupport.Message.VoIPWhileRecording"));
                    return;
                }

                if (this.mVoIPMsgInfo != null)
                    return;

                this.ShowVoIPRequest();
                //this.frmChat.ShowPanelVoIP(ViewType.VoIPLink);
            }
        }

        public void ConnectMsgHandle(string msg)
        {

            Form form = this;
            if (this.frmChat != null && this.frmChat.ShowInTaskbar == true && this.frmChat.WindowState != FormWindowState.Minimized)
                form = this.frmChat;

            RsCommon.ShowSplashBox(form, msg, true);
        }

        private void btnChat_Click(object sender, EventArgs e)
        {
            this.mActionHelper.Add(tdActionModeKeyForRS.ConnectChatOpen);

            this.ShowChat(false);
        }

        private void SetControlState()
        {
            //显示控制面板
#if MAC
            this.chkRemoteControl.Hidden = false;
#else
            this.chkRemoteControl.Visible = true;
#endif
            this.btnAction.Visible = true;

            if (IsConnectEvent)
            {
                try
                {
#if MAC
                    this.chkRemoteRequest.State = RsAPI.IsControl(RSEvent.TargetDevice.company_info.id) ? NSCellStateValue.On : NSCellStateValue.Off;
#else
                    this.chkRemoteRequest.skChecked = RsAPI.IsControl(RSEvent.TargetDevice.company_info.id);
#endif
                }
                catch (Exception)
                {
#if MAC
                    this.chkRemoteRequest.State = NSCellStateValue.On;
#else
                    this.chkRemoteRequest.skChecked = true;
#endif
                }

                this.chkRemoteRequest.Enabled = RsAPI.IsControlEnable();
            }
            else
            {
                bool isEnable = false;

                if (RsAPI.IsMan())
                {
                    isEnable = RsAPI.IsControlEnable();
                }
                else if (RsAPI.IsNoMan())
                {
                    if (RsAPI.DeviceDeployInfo.account_id == RSEvent.TargetDevice.company_info.id)
                        isEnable = false;
                    else
                        isEnable = true;
                }
                else
                    isEnable = true;

#if MAC
                this.chkRemoteControl.State = RSEvent.TargetDevice.ControlEnable ? NSCellStateValue.On : NSCellStateValue.Off;
                this.chkRemoteControl.Enabled = isEnable;
#else
                this.chkRemoteControl.skChecked = RSEvent.TargetDevice.ControlEnable;
                this.chkRemoteControl.Enabled = isEnable;
#endif

                this.btnVoice.Visible = RSEvent.TargetDevice.VoiceEnable;

                if (this.btnVoice.Visible == false)
                    this.pnlSplite.Location = new Point(this.btnVoice.Left + this.btnVoice.Width, this.pnlSplite.Location.Y);
            }
        }

        private void SizeHandle(bool reFormLocation = false)
        {
            if (!this.IsConnectEvent)
            {
                //判断下面Action有没有隐藏
#if MAC
                if (!this.chkRemoteControl.Hidden)
#else
                if (this.chkRemoteControl.Visible)
#endif
                {
                    this.skSourceSize = new Size(mWindowWidth, 198).ToDPI(this.Dpi);
                }
                else
                {
                    this.skSourceSize = new Size(mWindowWidth, 170).ToDPI(this.Dpi);
                }

                //判断有没有打开Voice
                if (this.mOpenVoice)
                {
                    this.skSourceSize = new Size(this.Width, this.Height + this.btnVoiceContent.Size.Height);
                }
            }
            else
            {
                this.skSourceSize = new Size(mWindowWidth, 274).ToDPI(this.Dpi);
            }

            this.RefreshSize(reFormLocation);

            NotifyMgr.RefreshLocation();
        }



        private delegate void VoiceHandleDelegate(bool isOpen);
        public void VoiceHandle(bool isOpen)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new VoiceHandleDelegate(this.VoiceHandle), isOpen);
                return;
            }

            TimerMgr.Dispose(this.mTimerVoice);
            this.mTimerVoice = null;

            if (isOpen)
            {
                this.btnVoice.skIcon = MyResource.GetImage("rs_reject_4.png");
                this.btnVoice.skToolTip = this.Language.GetString("rs_end_speech");

                this.mTimerVoice = TimerMgr.Create(1, OnTimerVoice);

                this.frmChat.ShowPanelVoIP(iTong.Android.ViewType.VoIPTime);
            }
            else
            {
                this.btnVoiceContent.skText = string.Format(Utility.FormatDuration(0, true, true).ToString());
                this.btnVoice.skIcon = MyResource.GetImage("chat_btn_4_phone.png");
                this.btnVoice.skToolTip = this.Language.GetString("RemoteSupport.Button.Voip");

                this.frmChat.ShowPanelVoIP(iTong.Android.ViewType.None);
            }

            this.mOpenVoice = isOpen;
            this.btnVoiceContent.Visible = isOpen;

#if MAC
            if (isOpen)
            {
                this.btnVoiceContent.Location = new Point(0, this.pnlTarDevice.Height - this.btnVoiceContent.Height);
                this.btnVoiceContent.Size = new Size(this.pnlTarDevice.Width, this.btnVoiceContent.Height);

                this.pnlTarDeviceInfo.Location = new Point(0, 0);
                this.pnlTarDeviceInfo.Size = new Size(this.pnlTarDeviceInfo.Width, this.pnlTarDevice.Height - this.btnVoiceContent.Height);

                // 添加上边框作为分割线
                this.btnVoiceContent.skBorderType = skBorderType.Top;
                this.btnVoiceContent.skBorderStrokeColor = skColor.FromArgb(228, 230, 236); // #E4E6EC 浅灰色分割线
                this.btnVoiceContent.skBorderWidth = 1;
            }
            else
            {
                // btnVoiceContent隐藏时，pnlTarDeviceInfo恢复占据整个空间
                this.pnlTarDeviceInfo.Location = new Point(0, 0);
                this.pnlTarDeviceInfo.Size = new Size(this.pnlTarDeviceInfo.Width, this.pnlTarDevice.Height);

                // 移除边框
                this.btnVoiceContent.skBorderType = skBorderType.None;
            }
#endif

            this.SizeHandle();
        }

        private void OnTimerVoice()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(() =>
                {
                    this.OnTimerVoice();
                }));
            }
            else
            {
                if (this.mTimerVoice != null)
                {
                    this.btnVoiceContent.skText = string.Format(Utility.FormatDuration((long)this.mTimerVoice.TimePass, true, true).ToString());
                    this.frmChat.SetVoIPTime((long)this.mTimerVoice.TimePass);
                }
            }
        }

        public void CloseForm()
        {
            this.Close();
        }

        private void picUserImage_Click(object sender, EventArgs e)
        {
            this.lblUserName.skText = this.TargetDevice?.user_info.mail + (this.lblUserName.skText == this.TargetDevice?.user_info.mail ? "(Screen Paused)" : "");

            SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.DebugClick));
        }

        private void chkRemoteControl_CheckedChanged(object sender, EventArgs e)
        {
#if MAC
            this.TargetDevice.ControlEnable = this.chkRemoteControl.State == NSCellStateValue.On;
#else
            this.TargetDevice.ControlEnable = this.chkRemoteControl.skChecked;
#endif

            this.mActionHelper.Add(this.TargetDevice.ControlEnable ? tdActionModeKeyForRS.ConnectControlSelect : tdActionModeKeyForRS.ConnectControlDisselect);

            SocketMgr.SendMsgFromClient(RSEventType.ControlChange, this.TargetDevice);
        }

        private void ShowVoIPRequest()
        {
            this.mNeedSendMsg = false;
            this.VoIPMsgBoxClose();

            this.btnVoice.skIcon = MyResource.GetImage("rs_reject_4.png");
            this.btnVoice.skToolTip = this.Language.GetString("rs_end_speech");

            this.mVoIPMsgInfo = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_waiting_accept_voice_invitation")), this.Language.GetString("pc_rs_intro_title"), this.Language);
            this.mVoIPMsgInfo.FirstButton.Visible = false;
            //this.mVoIPMsgInfo.SecondButton.ButtonCheckTime = 30;
            this.mVoIPMsgInfo.FormDialog = false;
            this.mVoIPMsgInfo.TopMost = true;
            this.mVoIPMsgInfo.FormClose = VoIPMsgBoxClose;
            this.mVoIPMsgInfo.SecondButton.Click = VoIPMsgBoxClose;

            this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPOpen);

            this.frmChat.ShowPanelVoIP(iTong.Android.ViewType.VoIPLink);
            MsgBoxMgr.Show(null, this.mVoIPMsgInfo);
            this.SendVoIP(this.TargetDevice, skVoIPStatus.Call);
        }

        private void VoIPMsgBoxRetryHandle()
        {
            if (this.mVoIPMsgInfo != null && this.mReceVoIP == false)
            {
                this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPClose);

                //if (this.mVoIPMsgInfo.SecondButton.ButtonTimeout)
                //{
                //    this.mNeedSendMsg = false;
                //    this.VoIPMsgBoxClose();

                //    this.mVoIPMsgInfo = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("rs_voice_connection_timed_out")), this.Language.GetString("Common.Info"), this.Language);
                //    this.mVoIPMsgInfo.FirstButton.ButtonText = this.Language.GetString("Common.Retry");
                //    this.mVoIPMsgInfo.SecondButton.ButtonText = this.Language.GetString("Login.Button.iSee");
                //    this.mVoIPMsgInfo.FirstButton.ButtonRequestBeforeClose = ShowVoIPRequest;
                //    this.mVoIPMsgInfo.TopMost = true;
                //    this.mVoIPMsgInfo.FormDialog = false;
                //    MsgBoxMgr.Show(null, this.mVoIPMsgInfo);
                //}
                //else
                //{
                //this.VoIPMsgBoxClose();
                //}
            }
        }

        private void VoIPMsgBoxClose()
        {
            if (this.mVoIPMsgInfo != null && this.mReceVoIP == false)
            {
                this.mReceVoIP = true;

                if (this.mNeedSendMsg)
                {
                    this.CommonTimeTip(RecordTipType.VoIPCancle);
                    this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPClose);
                    this.SendVoIP(this.TargetDevice, skVoIPStatus.Cancel);
                    this.frmChat.ShowPanelVoIP(iTong.Android.ViewType.None);
                    this.VoiceHandle(false);
                }
            }

            MsgBoxMgr.Remove(this.VoIPMsgInfo);

            this.mVoIPMsgInfo = null;
            this.mNeedSendMsg = true;
            this.mReceVoIP = false;
        }

        private void SendVoIP(skDevice device, skVoIPStatus skVoIPStatus)
        {
            RSEvent arg = new RSEvent();
            arg.EventType = RSEventType.VoIP;
            arg.TargetDevice = device;
            arg.Code = (int)skVoIPStatus;
            SocketMgr.SendMsgFromClient(arg);
        }

        /// <summary>每隔1秒进行一次置顶操作</summary>
        private void OnTimerTopMost()
        {
            try
            {
                // 当需要置顶时执行，若是触发tooltip时应将IsNeedTopMost设置为false
                if (IsNeedTopMost)
                {
                    if (this.InvokeRequired)
                    {
                        this.BeginInvoke(new ThreadStart(() =>
                        {
                            this.OnTimerTopMost();
                        }));
                    }
                    else
                    {
                        MyAPI.SetWindowTopMost(this.Handle);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "OnTimerTopMost");
            }
        }

        /// <summary>鼠标移出控件的时候开启置顶</summary>
        private void OpenTopMostWithMouseLeave(object sender, EventArgs e)
        {
            IsNeedTopMost = true;
        }

        /// <summary>鼠标移进控件的时候关掉置顶</summary>
        private void CloseTopMostWithMouseEnter(object sender, EventArgs e)
        {
            IsNeedTopMost = false;
        }
    }
}
