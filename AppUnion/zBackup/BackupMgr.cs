﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public static class BackupExtend
    {
        public static string GetPathOnPC(this MBFileSearchResult result, string fileNameOnPhone)
        {
            string strPath = string.Empty;

            if (result != null)
            {
                foreach (MBFileRecord record in result.ListData)
                {
                    if (record.PathOnPhone.EndsWith(fileNameOnPhone))
                    {
                        strPath = record.PathOnPhone;
                        break;
                    }
                }
            }

            return strPath;
        }
    }

    public class BackupKeyword
    {
        public List<string> ListCallHistory = new List<string>();

        public List<string> ListSMS = new List<string>();

        public List<string> ListContact = new List<string>();

        public List<string> ListNote = new List<string>();
    }

    public class BackupKeywordResult
    {
        public List<dbCallHistoryRecord> ListCallHistory = new List<dbCallHistoryRecord>();

        public List<SMSGroup> ListSMS = new List<SMSGroup>();

        public List<Contact> ListContact = new List<Contact>();

        public List<Note> ListNote = new List<Note>();

        public MBErrorType Type = MBErrorType.None;

        public string ErrMsg { get; set; } = string.Empty;

        public bool SetError(MBFileSearchResult result)
        {
            if (result.Type == MBErrorType.None)
                return false;

            this.Type = result.Type;
            this.ErrMsg = result.ErrMsg;

            return true;
        }

    }

    public class BackupMgr
    {
        private static object mLocker = new object();

        public static BackupKeywordResult FilterData(string dirBackup, string password, BackupKeyword keyword)
        {
            BackupKeywordResult keywordResult = new BackupKeywordResult();

            lock (mLocker)
            {
                MBFileSearchResult searchResult = null;
                MBFileSearch search = null;

                try
                {
                    /*
                    cd6702cea29fe89cf280a76794405adb17f9a0ee	HomeDomain	Library/AddressBook/AddressBookImages.sqlitedb
                    31bb7ba8914766d4ba40d6dfb6113c8b614be442	HomeDomain	Library/AddressBook/AddressBook.sqlitedb
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/AddressBook/AddressBook" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.SetError(searchResult))
                        goto DoExit;

                    if (searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("AddressBook.sqlitedb");
                        string dbImage = searchResult.GetPathOnPC("AddressBookImages.sqlitedb");

                        dbAddressBook db = dbAddressBook.Create(dbPath, dbImage);
                        keywordResult.ListContact = db.GetContacts(keyword.ListContact);
                    }

                    /*
                    5a4935c78a5255723f707230a451d79c540d2741	HomeDomain	Library/CallHistoryDB/CallHistory.storedata
                    a3388bc3048d99c38f0f6c485177ebcc64de0514	HomeDomain	Library/CallHistoryDB/com.apple.callhistory.databaseInfo.plist
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/CallHistoryDB/CallHistory" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.SetError(searchResult))
                        goto DoExit;

                    if (searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("CallHistory.storedata");

                        dbCallHistory db = dbCallHistory.Create(dbPath);
                        keywordResult.ListCallHistory = db.GetCallHistorys(keyword.ListCallHistory);
                    }

                    /*
                     3d0d7e5fb2ce288813306e4d4636395e047a3d28	HomeDomain	Library/SMS/sms.db
                     */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/SMS/sms.db" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.SetError(searchResult))
                        goto DoExit;

                    if (searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("sms.db");

                        dbSMS db = dbSMS.Create(dbPath);
                        keywordResult.ListSMS = db.GetMessages(keyword.ListSMS);
                    }

                    /*
                    ca3bc056d4da0bbf88b5fb3be254f3b7147e639c	HomeDomain	Library/Notes/notes.sqlite
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/Notes/notes.sqlite" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.SetError(searchResult))
                        goto DoExit;

                    if (searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("notes.sqlite");

                        dbNotes db = dbNotes.Create(dbPath);
                        keywordResult.ListNote = db.GetNotes(keyword.ListSMS);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "BackupMgr.Load");
                }
            }

        DoExit:
            return keywordResult;
        }
    }
}
