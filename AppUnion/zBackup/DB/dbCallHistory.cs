﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public class dbCallHistory : IDisposable
    {
        private static object mLocker = new object();
        private static Dictionary<string, dbCallHistory> mDictDB = new Dictionary<string, dbCallHistory>();

        private SQLiteConnection mConn = null;
        private string mTempFolder = "";

        private string mDbPathOnBackupFolder = "";
        private string mDbPathOnTempFolder = "";

        public dbCallHistory(string strDBFilePath)
        {
            try
            {
                this.mDbPathOnBackupFolder = strDBFilePath;

                this.mTempFolder = Folder.GetTempFilePath();
                Folder.CheckFolder(this.mTempFolder);

                this.mDbPathOnTempFolder = Path.Combine(this.mTempFolder, "call_history.db");               

                string dbName = "call_history.db";
                string dbNameShm = "call_history.db-shm";
                string dbNameWal = "call_history.db-wal";

                this.mDbPathOnTempFolder = Path.Combine(this.mTempFolder, dbName);

                string pathShmOnTemp = Path.Combine(this.mTempFolder, dbNameShm);
                string pathWalOnTemp = Path.Combine(this.mTempFolder, dbNameWal);

                string pathShmOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameShm);
                string pathWalOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameWal);

                if (File.Exists(this.mDbPathOnTempFolder))
                    File.Delete(this.mDbPathOnTempFolder);

                if (File.Exists(this.mDbPathOnBackupFolder))
                    File.Copy(this.mDbPathOnBackupFolder, this.mDbPathOnTempFolder);

                if (File.Exists(pathShmOnTemp))
                    File.Delete(pathShmOnTemp);

                if (File.Exists(pathShmOnBackup))
                    File.Copy(pathShmOnBackup, pathShmOnTemp);

                if (File.Exists(pathWalOnTemp))
                    File.Delete(pathWalOnTemp);

                if (File.Exists(pathWalOnBackup))
                    File.Copy(pathWalOnBackup, pathWalOnTemp);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbCallHistory.New");
            }
        }

        public static dbCallHistory Create(string strDBFilePath)
        {
            //string strID = strDBFilePath.Replace("/", "").Replace(@"\", "").Replace(" ", "").Replace(".", "").Replace(":", "");
            string strID = Common.GetMd5Hex(strDBFilePath);

            try
            {
                lock (mLocker)
                {
                    if (!string.IsNullOrEmpty(strID) && mDictDB.ContainsKey(strID))
                    {
                        dbCallHistory db = mDictDB[strID];
                        if (db == null)
                        {
                            mDictDB.Remove(strID);

                            db = new dbCallHistory(strDBFilePath);
                            mDictDB.Add(strID, db);
                        }
                    }
                    else
                    {
                        dbCallHistory db = new dbCallHistory(strDBFilePath);
                        mDictDB.Add(strID, db);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbCallHistory.Create");
            }

            return mDictDB[strID];
        }

        public bool Open()
        {
            bool blnResult = false;

            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if (File.Exists(this.mDbPathOnTempFolder))
                {
                    this.mConn = SQLiteClass3.CreateConnectionFromFile(this.mDbPathOnTempFolder);
                    if (this.mConn != null)
                        blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbCallHistory.Open");
            }

        DoExit:
            return blnResult;
        }

        public void Close()
        {
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                    this.mConn.Close();
            }
            catch
            { }
        }

        public void Dispose()
        {
            try
            {
                this.Close();
                GC.SuppressFinalize(this);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "dbCallHistory.Dispose");
            }
        }

        public List<dbCallHistoryRecord> GetCallHistorys(List<string> listPhoneNumber = null, int duration = 0)
        {
            List<dbCallHistoryRecord> listInfo = new List<dbCallHistoryRecord>();

            try
            {
                if (!this.Open())
                    goto DoExit;

                string strSQL = "SELECT Z_PK as ROWID,ZADDRESS as address,ZDATE as date,ZDURATION as duration,ZORIGINATED as flags,ZISO_COUNTRY_CODE as country_code FROM ZCALLRECORD ORDER BY date desc";

                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                if (dt == null)
                {
                    Common.Log("GetCallHistorys.ExecuteSQL -> dt = null " + strSQL, "Backup", true);
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    dbCallHistoryRecord callInfo = new dbCallHistoryRecord();
                    callInfo.CallHistoryROWID = Common.GetValue<string>(row["ROWID"], string.Empty);
                    if (row["address"] is byte[])
                    {
                        byte[] bufferAddress = Common.GetValue<byte[]>(row["address"], null);
                        if (bufferAddress != null)
                            callInfo.PhoneNumber = System.Text.Encoding.Default.GetString(bufferAddress);
                    }
                    else
                    {
                        callInfo.PhoneNumber = Common.GetValue<string>(row["address"], string.Empty);
                    }

                    if (row["date"] != null && !(row["date"] is System.DBNull))
                    {
                        long longDate = 0;
                        string dateStr = System.Convert.ToString(row["date"]);
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            int index = dateStr.IndexOf(".");
                            if (index > -1)
                                dateStr = dateStr.Substring(0, index);

                            if (long.TryParse(dateStr, out longDate))
                                callInfo.CreateDate = Common.ConvertToPcTime(longDate, DateTime.Parse("2001-01-01 08:00"));
                        }
                    }

                    callInfo.Duration = Common.GetValue<int>(row["duration"], 0);

                    int intValue = Common.GetValue<int>(row["flags"], 0);
                    if (intValue == 4 || intValue == 0 || intValue == 1769472)
                        callInfo.CallInOut = dbCallInOut.CallIn;
                    else
                        callInfo.CallInOut = dbCallInOut.CallOut;

                    callInfo.CountryCode = Common.GetValue<int>(row["country_code"], 0);

                    if (listPhoneNumber != null || duration > 0)
                    {
                        if (duration > 0 && callInfo.Duration > duration)
                        {
                            listInfo.Add(callInfo);
                        }
                        else
                        {
                            string phoneNumberReplace = callInfo.PhoneNumber.FormatPhoneNumber();
                            string phoneNumberFind = listPhoneNumber.Find((x) => phoneNumberReplace.Contains(x) || x.Contains(phoneNumberReplace));
                            if (!string.IsNullOrEmpty(phoneNumberFind))
                                listInfo.Add(callInfo);
                        }
                    }
                    else
                    {
                        listInfo.Add(callInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbCallHistory.GetCallHistorys");
            }

        DoExit:
            this.Close();

            return listInfo;
        }
    }

    public class dbCallHistoryRecord
    {
        public string CallHistoryROWID = "";
        public string PhoneNumber = "";
        public string Name = "";
        public DateTime CreateDate;
        public int Duration = 0;
        // Public CallFlags As Integer = 0
        public dbCallInOut CallInOut = dbCallInOut.CallIn;
        public int CountryCode = 460;
        public bool IsFind = false;
    }

    public enum dbCallInOut
    {
        CallIn,
        CallOut
    }
}
