﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;
using System.Text;


using iTong.CoreFoundation;
using iTong.Device;

#if MAC
using Image = AppKit.NSImage;
using SQLiteFunction = Mono.Data.Sqlite.SqliteFunction;
using SQLiteFunctionAttribute = Mono.Data.Sqlite.SqliteFunctionAttribute;
using FunctionType = Mono.Data.Sqlite.FunctionType;
#else
using System.Drawing;
using System.Data.SQLite;
using SQLiteConnection = System.Data.SQLite3.SQLiteConnection;
#endif

namespace iTong.CoreModule
{
    public class dbSMS : IDisposable
    {
        private static object mLocker = new object();
        private static Dictionary<string, dbSMS> mDictDB = new Dictionary<string, dbSMS>();

        private SQLiteConnection mConn = null;
        private string mTempFolder = "";
        private string mDbPathOnBackupFolder = "";
        private string mDbPathOnTempFolder = "";

#if MAC
        [SQLiteFunction(Arguments = 1, FuncType = Mono.Data.Sqlite.FunctionType.Scalar, Name = "read")]
#else
        [SQLiteFunction(Arguments = 1, FuncType = System.Data.SQLite.FunctionType.Scalar, Name = "read")]
#endif
        private class Read : SQLiteFunction
        {

        }

        private void RegFunc()
        {
            SQLiteFunction.RegisterFunction(typeof(Read));
        }


        public string TempPath
        {
            get
            {
                return mTempFolder;
            }
        }

        public dbSMS(string strDBFilePath)
        {
            try
            {
                this.mDbPathOnBackupFolder = strDBFilePath;

                this.mTempFolder = Folder.GetTempFilePath();
                Folder.CheckFolder(this.mTempFolder);

                string strMsmDBName = "sms.db";
                if (this.mDbPathOnBackupFolder.Length > 0)
                    strMsmDBName = Path.GetFileName(this.mDbPathOnBackupFolder);

                string dbName = strMsmDBName;
                string dbNameShm = strMsmDBName + "-shm";
                string dbNameWal = strMsmDBName + "-wal";

                this.mDbPathOnTempFolder = Path.Combine(this.mTempFolder, dbName);

                string pathShmOnTemp = Path.Combine(this.mTempFolder, dbNameShm);
                string pathWalOnTemp = Path.Combine(this.mTempFolder, dbNameWal);

                string pathShmOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameShm);
                string pathWalOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameWal);


                if (File.Exists(this.mDbPathOnTempFolder))
                    File.Delete(this.mDbPathOnTempFolder);

                if (File.Exists(this.mDbPathOnBackupFolder))
                    File.Copy(this.mDbPathOnBackupFolder, this.mDbPathOnTempFolder);

                if (File.Exists(pathShmOnTemp))
                    File.Delete(pathShmOnTemp);

                if (File.Exists(pathShmOnBackup))
                    File.Copy(pathShmOnBackup, pathShmOnTemp);

                if (File.Exists(pathWalOnTemp))
                    File.Delete(pathWalOnTemp);

                if (File.Exists(pathWalOnBackup))
                    File.Copy(pathWalOnBackup, pathWalOnTemp);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.New");
            }
        }

        public static dbSMS Create(string strDBFilePath)
        {
            //string strID = strDBFilePath.Replace("/", "").Replace(@"\", "").Replace(" ", "").Replace(".", "").Replace(":", "");
            string strID = Common.GetMd5Hex(strDBFilePath);

            try
            {
                lock (mLocker)
                {
                    if (!string.IsNullOrEmpty(strID) && mDictDB.ContainsKey(strID))
                    {
                        dbSMS db = mDictDB[strID];
                        if (db == null)
                        {
                            mDictDB.Remove(strID);

                            db = new dbSMS(strDBFilePath);
                            mDictDB.Add(strID, db);
                        }
                    }
                    else
                    {
                        dbSMS db = new dbSMS(strDBFilePath);
                        mDictDB.Add(strID, db);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.Create");
            }

            return mDictDB[strID];
        }

        private bool Open()
        {
            bool blnResult = false;

            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if (File.Exists(this.mDbPathOnTempFolder))
                {
                    this.mConn = SQLiteClass3.CreateConnectionFromFile(this.mDbPathOnTempFolder);
                    if (this.mConn != null)
                    {
                        blnResult = true;
                        this.RegFunc();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.Open");
            }

        DoExit:
            return blnResult;
        }

        public void Close()
        {
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                    this.mConn.Close();
            }
            catch
            { }
        }

        public void Dispose()
        {
            try
            {
                this.Close();
                GC.SuppressFinalize(this);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "dbSMS.Dispose");
            }
        }

        public List<SMSGroup> GetMessages(List<string> listKeywords = null)
        {
            List<SMSGroup> listInfo = new List<SMSGroup>();

            try
            {
                if (!this.Open())
                    goto DoExit;

                string strSQL = "SELECT * FROM chat";
                string strError = "";

                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn, ref strError);
                if (dt == null)
                {
                    Common.Log("GetMessages.ExecuteSQL -> dt = null " + strSQL + "\t" + strError, "Backup", true);
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    SMSGroup groupInfo = new SMSGroup();
                    groupInfo.GroupROWID = Common.GetValue<string>(row["ROWID"], string.Empty);
                    groupInfo.GroupID = Common.GetValue<string>(row["chat_identifier"], string.Empty);
                    groupInfo.PhoneNumber = Common.GetValue<string>(row["chat_identifier"], string.Empty);
                    groupInfo.Country = "cn";
                    groupInfo.Messages = this.LoadMessage(groupInfo, listKeywords);

                    SMSGroup group = this.GetGroupByPhoneNumber(groupInfo.PhoneNumber, listInfo);
                    if (group == null)
                        listInfo.Add(groupInfo);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.LoadGroup_6X");
            }

            // 排序
            List<SMSGroup> listInfoSort = new List<SMSGroup>();
            foreach (SMSGroup Item in listInfo)
            {
                if (listInfoSort.Count <= 0)
                {
                    listInfoSort.Add(Item);
                }
                else
                {
                    int index = 0;
                    foreach (SMSGroup Item1 in listInfoSort)
                    {
                        if (Item1.GroupDate < Item.GroupDate)
                            break;

                        index += 1;
                    }

                    listInfoSort.Insert(index, Item);
                }
            }

            listInfo = listInfoSort;

        DoExit:
            this.Close();

            return listInfo;
        }

        private SMSGroup GetGroupByPhoneNumber(string strPhoneNumber, List<SMSGroup> listInfo)
        {
            SMSGroup group = null;
            foreach (SMSGroup Item in listInfo)
            {
                if (Item.PhoneNumber == strPhoneNumber)
                {
                    group = Item;
                    break;
                }
            }

            return group;
        }

        private string FormatAddressLike(string strPhoneNumber)
        {
            string strReturn = "%";

            if (strPhoneNumber.Trim().Length > 0)
            {
                string[] strNumberSplit = strPhoneNumber.Split(new char[] { '-', ' ' });
                foreach (string Item in strNumberSplit)
                    strReturn += Item.Replace("+86", "") + "%";
            }

            strReturn = strReturn.TrimEnd('%');

            return strReturn;
        }

        private char[] GetSpecialChat()
        {
            byte[] bytess = new byte[] { 239, 191, 188 };
            return System.Text.Encoding.UTF8.GetString(bytess).ToCharArray();
        }

        private Image GetPicFormData(byte[] buffer)
        {
            // 从资料库里取图片。
            Image img = null;

            try
            {
                if (buffer != null)
                {
                    MemoryStream stream = new MemoryStream(buffer);
                    img = Image.FromStream(stream);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.GetPicFormData");
            }

            return img;
        }

        private List<SMSMessage> LoadMessage(SMSGroup group, List<string> listKeywords = null)
        {
            List<SMSMessage> listInfo = new List<SMSMessage>();

            try
            {
                if (this.mConn == null)
                    goto DoExit;

                string strSQL = string.Format("SELECT A.* FROM message A " +
                                              "Left Join chat_message_join B ON A.ROWID=B.message_id " +
                                              "Left Join chat C ON C.ROWID=B.chat_id " +
                                              "WHERE C.chat_identifier like '{0}' ORDER BY date;", this.FormatAddressLike(group.PhoneNumber));

                string strSqlAtt = "SELECT * FROM attachment";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                DataTable dtAtt = SQLiteClass3.ExecuteSQL(strSqlAtt, this.mConn);

                if (dt == null || dt.Rows.Count == 0)
                    goto DoExit;

                foreach (DataRow row in dt.Rows)
                {
                    SMSMessage smsInfo = new SMSMessage();
                    smsInfo.MessageROWID = Common.GetValue<string>(row["ROWID"], string.Empty);
                    smsInfo.GroupID = Common.GetValue<string>(row["account_guid"], string.Empty);
                    smsInfo.Country = Common.GetValue<string>(row["country"], string.Empty);
                    smsInfo.PhoneNumber = group.PhoneNumber; // Common.GetValue(Of String)(row("address"), String.Empty)

                    if (row["date"] != null && !(row["date"] is System.DBNull))
                    {
                        long longDate = 0;
                        string dateStr = System.Convert.ToString(row["date"]);
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            int index = dateStr.IndexOf(".");
                            if (index > -1)
                                dateStr = dateStr.Substring(0, index);

                            // 兼容ios11短信时间
                            dateStr = dateStr.Substring(0, 9);

                            if (long.TryParse(dateStr, out longDate))
                                smsInfo.CreateDate = Common.ConvertToPcTime(longDate, DateTime.Parse("2001-01-01 08:00"));
                        }
                    }

                    if (group.GroupDate < smsInfo.CreateDate)
                        group.GroupDate = smsInfo.CreateDate;

                    smsInfo.Content = Common.GetValue<string>(row["text"], string.Empty).Trim(this.GetSpecialChat());
                    smsInfo.IsSend = (Common.GetValue<long>(row["is_sent"], 0) == 1);
                    smsInfo.Read = (Common.GetValue<long>(row["is_read"], 0) == 1);

                    if (smsInfo.IsSend == true)
                        smsInfo.IsSend = (Common.GetValue<long>(row["is_from_me"], 0) == 1);

                    if (Common.GetValue<long>(row["cache_has_attachments"], 0) == 1)
                    {
                        string strServer = Common.GetValue<string>(row["service"], string.Empty);
                        this.LoadMessagePic(row, dtAtt, smsInfo, strServer);
                    }

                    if (listKeywords == null)
                    {
                        listInfo.Add(smsInfo);
                    }
                    else
                    {
                        string phoneNumberReplace = smsInfo.PhoneNumber.FormatPhoneNumber();
                        string findText = listKeywords.Find((x) => smsInfo.Content.Contains(x) || x.Contains(smsInfo.Content) || phoneNumberReplace.Contains(x) || x.Contains(phoneNumberReplace));
                        if (!string.IsNullOrEmpty(findText))
                            listInfo.Add(smsInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.LoadMessage_6X");
            }

        DoExit:
            return listInfo;
        }

        private void LoadMessagePic(DataRow row, DataTable dtAtt, SMSMessage smsInfo, string server)
        {
            byte[] att = Common.GetValue(row["attributedBody"], new byte[] { });

            SMSMessagePic pic = new SMSMessagePic();
            pic.PicID = this.GetPicAddressString(att);

            string strFilter = string.Format("guid='{0}'", pic.PicID);
            DataRow[] arrRows = dtAtt.Select(strFilter, "ROWID ASC");
            if (arrRows != null && arrRows.Length > 0)
            {
                string[] arrType = Common.GetValue(arrRows[0]["mime_type"], "").Split('/');

                pic.Type = arrType[arrType.Length - 1];

                //if (this.mDevice == null)
                {
                    string pathOnPhone = Common.GetValue(arrRows[0]["filename"], "");
                    pic.PicPath = pathOnPhone.Replace("~/Library/SMS/", "/var/mobile/Library/SMS/");
                    pic.PicPathOnPC = Path.Combine(mTempFolder, pathOnPhone.Replace("~/Library/SMS/", "").Replace('/', Path.DirectorySeparatorChar));
                    pic.PartID = "-1";
                }
                //else
                //{
                //    pic.PicPath = Path.Combine(mTempFolder, Common.GetValue(arrRows[0]["filename"], "").Replace("~/Library/SMS/", "").Replace("/", @"\"));
                //    pic.PicPathOnPC = pic.PicPath;
                //    pic.PartID = "-1";
                //}

                pic.Class = ImageVideo.Image;
                smsInfo.Content += string.Format("#Pic{0}#", 1);

                if (smsInfo.Pics == null)
                    smsInfo.Pics = new List<SMSMessagePic>();

                smsInfo.Pics.Add(pic);
            }
        }

        private string GetPicAddressString(byte[] att)
        {
            string strReturn = "";
            try
            {
                if (att == null || att.Length <= 0)
                    return strReturn;
                string strKey = "GUIDAttributeName";
                string strArr = System.Text.Encoding.ASCII.GetString(att);
                int indexGUI = strArr.IndexOf(strKey);
                if (indexGUI >= 0)
                {
                    int intStart = indexGUI + strKey.Length;
                    foreach (char chr in strArr.ToCharArray(intStart, strArr.Length - intStart))
                    {
                        if ((chr >= '0' && chr <= '9') || (chr >= 'a' && chr <= 'f') || (chr >= 'A' && chr <= 'F') || chr == '-')
                        {
                            strReturn += chr;
                            if (strReturn.Length >= 36)
                                break;
                        }
                    }
                }
                if (strReturn.Length == 0)
                {
                    strKey = "NSString";
                    strArr = System.Text.Encoding.ASCII.GetString(att);
                    indexGUI = strArr.IndexOf(strKey);
                    if (indexGUI >= 0)
                    {
                        int intStart = indexGUI + strKey.Length;
                        foreach (char chr in strArr.ToCharArray(intStart, strArr.Length - intStart))
                        {
                            if ((chr >= '0' && chr <= '9') || (chr >= 'a' && chr <= 'f') || (chr >= 'A' && chr <= 'F') || chr == '-')
                            {
                                strReturn += chr;
                                if (strReturn.Length >= 36)
                                    break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbSMS.GetPicAddressString");
            }

            return strReturn;
        }

    }
}
