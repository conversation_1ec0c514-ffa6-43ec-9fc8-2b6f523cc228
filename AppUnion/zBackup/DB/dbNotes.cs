﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public class dbNotes : IDisposable
    {
        private static object mLocker = new object();
        private static Dictionary<string, dbNotes> mDictDB = new Dictionary<string, dbNotes>();

        private SQLiteConnection mConn = null;
        private string mTempFolder = "";
        private string mDbPathOnBackupFolder = "";
        private string mDbPathOnTempFolder = "";

        public dbNotes(string strDBFilePath)
        {
            try
            {
                this.mDbPathOnBackupFolder = strDBFilePath;

                this.mTempFolder = Folder.GetTempFilePath();
                Folder.CheckFolder(this.mTempFolder);

                string dbName = "notes.sqlite";
                string dbNameShm = "notes.sqlite-shm";
                string dbNameWal = "notes.sqlite-wal";

                this.mDbPathOnTempFolder = Path.Combine(this.mTempFolder, dbName);

                string pathShmOnTemp = Path.Combine(this.mTempFolder, dbNameShm);
                string pathWalOnTemp = Path.Combine(this.mTempFolder, dbNameWal);

                string pathShmOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameShm);
                string pathWalOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameWal);


                if (File.Exists(this.mDbPathOnTempFolder))
                    File.Delete(this.mDbPathOnTempFolder);

                if (File.Exists(this.mDbPathOnBackupFolder))
                    File.Copy(this.mDbPathOnBackupFolder, this.mDbPathOnTempFolder);

                if (File.Exists(pathShmOnTemp))
                    File.Delete(pathShmOnTemp);

                if (File.Exists(pathShmOnBackup))
                    File.Copy(pathShmOnBackup, pathShmOnTemp);

                if (File.Exists(pathWalOnTemp))
                    File.Delete(pathWalOnTemp);

                if (File.Exists(pathWalOnBackup))
                    File.Copy(pathWalOnBackup, pathWalOnTemp);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbNotes.New");
            }
        }

        public static dbNotes Create(string strDBFilePath)
        {
            //string strID = strDBFilePath.Replace("/", "").Replace(@"\", "").Replace(" ", "").Replace(".", "").Replace(":", "");
            string strID = Common.GetMd5Hex(strDBFilePath);

            try
            {
                lock (mLocker)
                {
                    if (!string.IsNullOrEmpty(strID) && mDictDB.ContainsKey(strID))
                    {
                        dbNotes db = mDictDB[strID];
                        if (db == null)
                        {
                            mDictDB.Remove(strID);

                            db = new dbNotes(strDBFilePath);
                            mDictDB.Add(strID, db);
                        }
                    }
                    else
                    {
                        dbNotes db = new dbNotes(strDBFilePath);
                        mDictDB.Add(strID, db);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbNotes.Create");
            }

            return mDictDB[strID];
        }

        public bool Open()
        {
            bool blnResult = false;
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if (File.Exists(this.mDbPathOnTempFolder))
                {
                    this.mConn = SQLiteClass3.CreateConnectionFromFile(this.mDbPathOnTempFolder);
                    if (this.mConn != null)
                        blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbNotes.Open");
            }

        DoExit:
            return blnResult;
        }

        public void Close()
        {
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                    this.mConn.Close();
            }
            catch
            { }
        }

        public void Dispose()
        {
            try
            {
                this.Close();
                GC.SuppressFinalize(this);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "dbNotes.Dispose");
            }
        }

        public List<Note> GetNotes(List<string> listKeywords = null)
        {
            List<Note> listInfo = new List<Note>();
            try
            {
                if (!this.Open())
                    goto DoExit;

                string strSQL = "SELECT A.ZAUTHOR,A.ZCONTENTTYPE,A.ZCREATIONDATE,A.ZMODIFICATIONDATE,A.ZTITLE,B.ZCONTENT From ZNOTE A Left Join ZNOTEBODY B ON A.ZBODY=B.Z_PK;";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                if (dt == null)
                {
                    Common.Log("GetNotes.ExecuteSQL -> dt = null " + strSQL, "Backup", true);
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    Note info = new Note();
                    info.Author = Common.GetValue<string>(row["ZAUTHOR"], string.Empty);
                    info.Content = Common.GetValue<string>(row["ZCONTENT"], string.Empty);
                    info.ContentType = Common.GetValue<ContentType>(row["ZCONTENTTYPE"], ContentType.TextPlain);

                    info.DateCreated = this.GetDateTime(Common.GetValue<string>(row["ZCREATIONDATE"], string.Empty));
                    info.DateModified = this.GetDateTime(Common.GetValue<string>(row["ZMODIFICATIONDATE"], string.Empty));
                    info.Subject = Common.GetValue<string>(row["ZTITLE"], string.Empty);

                    if (listKeywords == null)
                    {
                        listInfo.Add(info);
                    }
                    else
                    {
                        string findText = listKeywords.Find((x) => info.Content.Contains(x) || x.Contains(info.Content) || info.Subject.Contains(x) || x.Contains(info.Subject));
                        if (!string.IsNullOrEmpty(findText))
                            listInfo.Add(info);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbNotes.LoadNotes");
            }

        DoExit:
            this.Close();

            return listInfo;
        }

        private DateTime GetDateTime(string strValue)
        {
            double dbValue = Common.GetDigit(strValue);

            return Common.ConvertToPcTime(Convert.ToInt64(dbValue)); ;
        }

    }
}
